from services.rag_service import RagService
from services.chat_service import Cha<PERSON><PERSON><PERSON><PERSON>
from services.qdrant_service import QdrantService
from utils.llm_utils import call_gemini
from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Any, Generator, Union, AsyncGenerator
import asyncio

class QueryType(Enum):
    CONVERSATIONAL = "conversational"
    DATABASE = "database"
    KNOWLEDGE = "knowledge"

@dataclass
class ClassificationResult:
    query_type: QueryType
    confidence: float
    explanation: str = ""
    requires_db: bool = False

class RouterService:
    """Service for routing queries to appropriate handlers"""
    
    def __init__(self):
        self.rag_service = RagService()
        self.chat_service = ChatService()
        self.qdrant_service = QdrantService()

    async def classify_query(self, user_query: str, max_tokens: int = 100) -> ClassificationResult:
        """
        Asynchronously classifies the user query to determine the appropriate handler.
        Returns a detailed classification result.
        """
        # Check for simple conversational queries first
        if self._is_simple_greeting(user_query):
            return ClassificationResult(
                query_type=QueryType.CONVERSATIONAL,
                confidence=1.0,
                explanation="Simple greeting detected",
                requires_db=False
            )
        
        # Get similar router examples based on the user query
        similar_examples = await self.qdrant_service.retrieve_similar_router_examples(user_query)
        
        # Format examples for the prompt
        examples_text = ""
        if similar_examples:
            for example in similar_examples:
                examples_text += f"""---
                [USER_QUERY]
                {example['user_query']}
                [RESPONSE]
                {example['response_full_text']}
                ---\n\n"""
                        
        # Get router instructions
        router_instructions = await self.qdrant_service.fetch_router_instructions()
        
        # Build the prompt with instructions first, then similar examples
        prompt = f"""
        {router_instructions}

        ### Similar Examples:
        {examples_text}

        [USER_QUERY]
        {user_query}
        [RESPONSE]
        """
        
        # Print the full prompt for debugging
        print("\n=== Router Classification Prompt ===")
        print(prompt)
        print("=== End Router Classification Prompt ===\n")
        
        # Call Gemini asynchronously - no need for to_thread since call_gemini is now async
        result = await call_gemini(prompt, max_tokens=max_tokens)
        
        # Parse the response
        lines = result.strip().split('\n')
        score = 0
        requires_db = False
        
        for line in lines:
            if line.startswith('Score:'):
                try:
                    score = int(line.replace('Score:', '').strip())
                except ValueError:
                    score = 0
            elif line.startswith('Requires DB:'):
                db_value = line.replace('Requires DB:', '').strip().upper()
                requires_db = db_value == "DB" or db_value == "YES"
        
        # Determine query type based on DB requirement only
        if requires_db:
            query_type = QueryType.DATABASE
        else:
            query_type = QueryType.KNOWLEDGE
        
        # Create the classification result
        classification = ClassificationResult(
            query_type=query_type,
            confidence=float(score) / 5.0,  # Normalize to 0-1 range
            explanation=f"Score: {score}, Requires DB: {'Yes' if requires_db else 'No'}",
            requires_db=requires_db
        )
        
        return classification

    def _is_simple_greeting(self, query: str) -> bool:
        """Check if the query is a simple greeting or conversational phrase"""
        query = query.lower().strip()
        
        # List of common greetings and conversational starters
        greetings = [
            'hello', 'hi', 'hey', 'greetings', 'good morning', 'good afternoon', 
            'good evening', 'howdy', 'what\'s up', 'how are you', 'how\'s it going',
            'nice to meet you', 'pleased to meet you', 'yo', 'hiya'
        ]
        
        # Check if the query is just a greeting
        for greeting in greetings:
            # Exact match or greeting with punctuation
            if query == greeting or query == greeting + '?' or query == greeting + '!':
                return True
        
        # Check if it's a very short query (1-3 words) that contains a greeting
        if len(query.split()) <= 3:
            for greeting in greetings:
                if greeting in query:
                    return True
        
        return False

    async def route_query(self, user_query: str, max_tokens: int = 512):
        """
        Routes a user query to the appropriate service based on classification.
        Now fully async with proper handling of async generators.
        """
        try:
            # Classify the query
            classification = await self.classify_query(user_query)
            print(f"\n=== Router Classification ===")
            print(f"Query: {user_query}")
            print(f"Classification: {classification.query_type.name}")
            print(f"Confidence: {classification.confidence}")
            print(f"Requires DB: {classification.requires_db}")
            
            # Route based on classification
            if classification.query_type == QueryType.DATABASE:
                print("Routing to database handler - Query is specific")
                # For backward compatibility, still yield strings for DB queries
                yield {"type": "text", "content": "DB"}
                yield {"type": "text", "content": user_query}
            elif classification.query_type == QueryType.CONVERSATIONAL:
                print("Routing to chat handler - Simple greeting detected")
                # Now chat_service is async, we can await it directly
                try:
                    async for chat_response in self.chat_service.handle_conversational(user_query, max_tokens):
                        yield chat_response
                except Exception as e:
                    print(f"Error in chat service: {str(e)}")
                    yield {"type": "text", "content": f"I'm sorry, I encountered an error while processing your request. Please try again later."}
            else:  # KNOWLEDGE type
                print("Routing to RAG handler")
                # Now rag_service is async, we can await it directly
                try:
                    async for rag_response in self.rag_service.handle_rag(user_query, max_tokens):
                        yield rag_response
                except Exception as e:
                    print(f"Error in RAG service: {str(e)}")
                    yield {"type": "text", "content": f"I'm sorry, I encountered an error while processing your request. Please try again later."}
        except Exception as e:
            print(f"Error in router service: {str(e)}")
            yield {"type": "text", "content": f"I'm sorry, I encountered an error while processing your request. Please try again later."}

