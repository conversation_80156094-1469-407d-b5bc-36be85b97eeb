<!DOCTYPE html>
<html data-bs-theme="dark">
<head>
    <title>FESCO Data Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #ff7700;
            --primary-gradient: linear-gradient(45deg, #ff7700, #ffbb00);
            --primary-hover: #ff8800;
            --bg-dark: #212529;
            --bg-darker: #1a1d20;
            --card-bg: #2c3034;
            --text-color: #e9ecef;
            --text-muted: #adb5bd;
            --border-color: #495057;
            --highlight-bg: rgba(255, 119, 0, 0.15);
        }
        
        body {
            background-color: var(--bg-dark);
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--text-color);
        }
        
        .main-container {
            max-width: 1000px;
            margin: 40px auto;
            padding: 20px;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 600;
        }
        
        .logo i {
            color: #ff7700;
        }
        
        .query-form {
            background-color: var(--card-bg);
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            margin-bottom: 30px;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }
        
        .query-form:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
        }
        
        .form-control {
            background-color: var(--bg-darker);
            border-color: var(--border-color);
            color: var(--text-color);
        }
        
        .form-control:focus {
            background-color: var(--bg-darker);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(255, 119, 0, 0.25);
            color: var(--text-color);
        }
        
        .btn-primary {
            background: var(--primary-gradient);
            border: none;
            padding: 10px 24px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary:hover {
            background: linear-gradient(45deg, #ff8800, #ffcc00);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        .result-section {
            background-color: var(--card-bg);
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            margin-top: 20px;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }
        
        .result-section:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
        }
        
        .analysis-content {
            white-space: pre-line;
            line-height: 1.7;
            color: var(--text-color);
        }
        
        .loading-indicator {
            text-align: center;
            margin: 40px 0;
        }
        
        .spinner-border {
            width: 3rem;
            height: 3rem;
            color: var(--primary-color);
        }
        
        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }
        
        .section-header i {
            margin-right: 10px;
            color: var(--primary-color);
        }
        
        .bullet-point {
            margin-left: 20px;
            position: relative;
            padding-left: 20px;
        }
        
        .bullet-point:before {
            content: "•";
            color: var(--primary-color);
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .highlight {
            background-color: var(--highlight-bg);
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-gradient);
            color: white;
            text-align: center;
            line-height: 40px;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.3s;
        }
        
        .back-to-top:hover {
            background: linear-gradient(45deg, #ff8800, #ffcc00);
            transform: translateY(-3px);
        }
        
        .progress {
            background-color: var(--bg-darker);
        }
        
        .progress-bar {
            background: var(--primary-gradient);
        }
        
        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
            background: transparent;
        }
        
        .btn-outline-primary:hover {
            background: var(--primary-gradient);
            border-color: transparent;
            color: white;
        }
        
        .btn-outline-success {
            color: #ffbb00;
            border-color: #ffbb00;
            background: transparent;
        }
        
        .btn-outline-success:hover {
            background: linear-gradient(45deg, #ffbb00, #ffdd00);
            border-color: transparent;
            color: #212529;
        }
        
        .alert-danger {
            background-color: rgba(220, 53, 69, 0.2);
            color: #ea868f;
            border-color: rgba(220, 53, 69, 0.3);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="logo">
            <h1><i class="bi bi-lightning-charge-fill"></i> FESCO Data Analysis</h1>
            <p class="text-muted">Analyze FESCO data with AI-powered insights</p>
        </div>
        
        <div class="query-form">
            <div class="section-header">
                <i class="bi bi-search fs-4"></i>
                <h4 class="mb-0">Ask Your Question</h4>
            </div>
            <form id="analysisForm">
                <div class="mb-3">
                    <label for="query" class="form-label">What would you like to analyze?</label>
                    <textarea 
                        class="form-control" 
                        id="query" 
                        rows="3" 
                        placeholder="Example: Analyze the distribution losses across different subdivisions"
                        required></textarea>
                </div>
                <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="bi bi-graph-up me-2"></i>Analyze Data
                    </button>
                </div>
            </form>
        </div>

        <div id="loadingIndicator" class="loading-indicator d-none">
            <div class="spinner-border" role="status" style="color: #ff7700;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3 fs-5">Analyzing FESCO data... This may take a minute.</p>
            <div class="progress mt-3" style="height: 10px;">
                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
            </div>
        </div>

        <div id="resultsContainer" class="d-none fade-in">
            <div class="section-header">
                <i class="bi bi-clipboard-data fs-4"></i>
                <h3 class="mb-0">Analysis Results</h3>
            </div>
            <div id="analysisResults" class="result-section">
                <div id="analysisContent" class="analysis-content"></div>
            </div>
            
            <div class="text-center mt-4">
                <button id="newQueryBtn" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-repeat me-2"></i>New Analysis
                </button>
                <button id="downloadBtn" class="btn btn-outline-success ms-2">
                    <i class="bi bi-download me-2"></i>Download Results
                </button>
            </div>
        </div>

        <div id="visualizationContainer" class="d-none fade-in mt-4">
            <div class="section-header">
                <i class="bi bi-graph-up fs-4"></i>
                <h3 class="mb-0">Data Visualization</h3>
            </div>
            <div class="card bg-dark border-secondary">
                <div class="card-body">
                    <div id="visualization" style="width: 100%; height: 500px;"></div>
                    <div id="visualizationError"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div id="backToTop" class="back-to-top d-none">
        <i class="bi bi-arrow-up"></i>
    </div>

    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>

    <script>
        document.getElementById('analysisForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const query = document.getElementById('query').value;
            const loadingIndicator = document.getElementById('loadingIndicator');
            const resultsContainer = document.getElementById('resultsContainer');
            const analysisContent = document.getElementById('analysisContent');
            const visualizationContainer = document.getElementById('visualizationContainer');
            
            // Clear previous results
            analysisContent.innerHTML = '';
            resultsContainer.classList.add('d-none');
            visualizationContainer.classList.add('d-none');
            document.getElementById('visualization').innerHTML = '';
            document.getElementById('visualizationError').innerHTML = '';
            
            // Show loading indicator
            loadingIndicator.classList.remove('d-none');
            
            try {
                const response = await fetch('/api/fesco-analysis', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ prompt: query })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Handle streaming response
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                while (true) {
                    const { value, done } = await reader.read();
                    
                    if (done) {
                        break;
                    }
                    
                    // Decode the chunk and split by newlines
                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n').filter(line => line.trim());
                    
                    for (const line of lines) {
                        try {
                            const result = JSON.parse(line);
                            
                            if (result.type === 'analysis' || result.type === 'text') {
                                // Format the content with enhanced styling
                                let formattedContent = result.content
                                    .replace(/•\s*(.*?)(?=\n|$)/g, '<div class="bullet-point">$1</div>')  // Style bullet points
                                    .replace(/\*\*(.*?)\*\*/g, '<strong style="color: #ffbb00;">$1</strong>')  // Bold text between ** with orange color
                                    .replace(/\n\s*([A-Z][A-Za-z\s]+:)/g, '<br><br><strong style="color: #ff7700;">$1</strong>')  // Section headers in orange
                                    .replace(/(\d+(\.\d+)?%)/g, '<span class="highlight">$1</span>')  // Highlight percentages
                                    .replace(/\n/g, '<br>'); // Replace newlines with <br>
                                
                                analysisContent.innerHTML += formattedContent;
                                
                                // Show results container as soon as we get the first chunk
                                if (resultsContainer.classList.contains('d-none')) {
                                    resultsContainer.classList.remove('d-none');
                                }
                            } else if (result.type === 'json') {
                                // Handle visualization JSON
                                try {
                                    const vizContent = JSON.parse(result.content);
                                    
                                    // Show visualization container
                                    visualizationContainer.classList.remove('d-none');
                                    
                                    // Create a new div for this visualization
                                    const vizDiv = document.getElementById('visualization');
                                    
                                    // Render the plot
                                    Plotly.newPlot(vizDiv, vizContent.data, vizContent.layout || {});
                                    console.log("Visualization rendered successfully");
                                } catch (e) {
                                    // Just log the error, don't show it to the user
                                    console.error("Error rendering visualization:", e);
                                    // No error message displayed to user
                                }
                            } else {
                                console.log('Received result of type:', result.type);
                            }
                        } catch (parseError) {
                            console.error('Error parsing JSON:', parseError, line);
                        }
                    }
                }
                
                // Show back to top button if content is long
                if (analysisContent.offsetHeight > 500) {
                    document.getElementById('backToTop').classList.remove('d-none');
                }
                
            } catch (error) {
                console.error('Error:', error);
                analysisContent.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        Error: ${error.message}
                    </div>`;
                resultsContainer.classList.remove('d-none');
            } finally {
                // Hide loading indicator
                loadingIndicator.classList.add('d-none');
            }
        });
        
        // New Query button handler
        document.getElementById('newQueryBtn').addEventListener('click', () => {
            document.getElementById('query').value = '';
            document.getElementById('resultsContainer').classList.add('d-none');
            document.getElementById('query').focus();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
        
        // Download results button handler
        document.getElementById('downloadBtn').addEventListener('click', () => {
            const content = document.getElementById('analysisContent').innerText;
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = 'fesco-analysis-results.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });
        
        // Back to top button handler
        window.addEventListener('scroll', () => {
            const backToTop = document.getElementById('backToTop');
            if (window.scrollY > 300) {
                backToTop.classList.remove('d-none');
            } else {
                backToTop.classList.add('d-none');
            }
        });
        
        document.getElementById('backToTop').addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    </script>
</body>
</html>



