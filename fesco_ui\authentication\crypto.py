import logging
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import padding
import binascii

# Configure logging
logging.basicConfig(level=logging.DEBUG)

# Assuming cryptoSecret is a dictionary with the necessary keys
cryptoSecret = {
        "algorithm": "aes-256-cbc",
        "secret": "aa886ea7670cc197f730591489db19f49ef924bfb7a2e733e2ec5419a6337d64",
        "iv": "my initialization vector"
    }

class CryptoService:
    def __init__(self):
        self.algorithm = cryptoSecret['algorithm']
        self.key = cryptoSecret['secret'][:32].encode('utf-8')  # Use first 32 bytes
        self.iv = cryptoSecret['iv'][:16].encode('utf-8')  # Use first 16 bytes

    def encrypt(self, plaintext: str) -> str:
        try:
            # Create a cipher object
            cipher = Cipher(algorithms.AES(self.key), modes.CBC(self.iv), backend=default_backend())
            encryptor = cipher.encryptor()

            # Pad the plaintext to be a multiple of the block size
            padder = padding.PKCS7(algorithms.AES.block_size).padder()
            padded_data = padder.update(plaintext.encode('utf-8')) + padder.finalize()

            # Encrypt the padded data
            encrypted_text = encryptor.update(padded_data) + encryptor.finalize()
            return binascii.hexlify(encrypted_text).decode('utf-8')
        except Exception as error:
            logging.error("Encryption error: %s", error)
            return None

    def decrypt(self, ciphertext: str) -> str:
        try:
            # Convert hex to bytes
            ciphertext_bytes = binascii.unhexlify(ciphertext)

            # Create a cipher object
            cipher = Cipher(algorithms.AES(self.key), modes.CBC(self.iv), backend=default_backend())
            decryptor = cipher.decryptor()

            # Decrypt the data
            decrypted_padded_data = decryptor.update(ciphertext_bytes) + decryptor.finalize()

            # Unpad the decrypted data
            unpadder = padding.PKCS7(algorithms.AES.block_size).unpadder()
            decrypted_data = unpadder.update(decrypted_padded_data) + unpadder.finalize()

            return decrypted_data.decode('utf-8')
        except Exception as error:
            logging.error("Decryption error: %s", error)
            return None

# Example usage
if __name__ == '__main__':
    crypto_service = CryptoService()
    encrypted = crypto_service.encrypt("Hello, World!")
    print("Encrypted:", encrypted)
    encrypted = "90a5c12243bc3ddfa0fa5bdb654f8c99137774d30b7bb56d7bb3be33d9b82882d26e7b145071b23d933a43a96ed216abcb0cca6e18a957d4ef3b65e6e6ab3834fbd6f5269494cd1bf4ec767c3bc357b79342607563033c2541bf7910f532765f2e9f5db15c4058ecd66997389d0ac53d47894002a98278fe6c4e9d5a1026e27514f64d24f34725e0e158b32cdf273877b742e428a2eb749e48960cefec6460444d94bb352d002eb5d10dcc8141ea58ef"
    decrypted = crypto_service.decrypt(encrypted)
    print("Decrypted:", decrypted)