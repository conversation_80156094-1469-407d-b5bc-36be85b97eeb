import asyncio
import pandas as pd
import sys
import os
from pathlib import Path

# Add the parent directory to sys.path to import from app
sys.path.append(str(Path(__file__).parent.parent.parent))

from app.services.analysis_service import AnalysisService

async def test_csv_analysis():
    """Test analyzing a local CSV file with the Analysis Service"""
    print("=== Testing CSV File Analysis ===")
    
    # Path to the CSV file - adjust this to your actual CSV location
    csv_path = Path(__file__).parent / "data" / "sample_data.csv"
    
    # Check if file exists
    if not csv_path.exists():
        print(f"CSV file not found at: {csv_path}")
        print("Please place a CSV file at this location or adjust the path.")
        return
    
    # Read the CSV file into a DataFrame
    print(f"\nReading CSV file from: {csv_path}")
    df = pd.read_csv(csv_path)
    
    # Print DataFrame info
    print("\nDataFrame Summary:")
    print(f"Shape: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")
    print("\nSample Data:")
    print(df.head())
    
    # Create a sample user query for analysis
    user_query = "Analyze this data and identify key patterns and insights"
    
    # Initialize the Analysis Service
    analysis_service = AnalysisService()
    
    # Prepare the data for analysis (similar to what happens in the service)
    num_rows = len(df)
    data_types = df.dtypes.to_dict()
    summary_stats = df.describe(include='all').to_dict()
    sample_data = df.head(5).to_dict(orient='records')
    columns = df.columns.tolist()
    
    # Print what would be sent to the model
    print("\n=== Data and Prompt Being Sent to Model ===")
    print(f"\nUser Query: {user_query}")
    print(f"\nDataFrame Details:")
    print(f"- Columns: {columns}")
    print(f"- Number of rows: {num_rows}")
    print(f"- Data types: {data_types}")
    print(f"- Sample rows: {sample_data}")
    
    # Test the actual analysis
    print("\n=== Generating Analysis ===")
    async for result in analysis_service.handle_analysis(df, user_query):
        print("\nAnalysis Result:")
        print(f"Type: {result['type']}")
        print(f"Content: {result['content']}")

if __name__ == "__main__":
    # Create the data directory if it doesn't exist
    data_dir = Path(__file__).parent / "data"
    data_dir.mkdir(exist_ok=True)
    
    # Run the test
    asyncio.run(test_csv_analysis())