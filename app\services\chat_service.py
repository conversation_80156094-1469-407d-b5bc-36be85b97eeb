import asyncio
from utils.llm_utils import call_gemini

class ChatService:
    """Service for handling conversational interactions"""
    
    async def handle_conversational(self, user_query: str, max_tokens: int = 512, temperature: float = 0.7):
        """
        Asynchronously yields the Gemini response for simple chit-chat queries.
        """
        try:
            system_prompt = (
                "You are ThunderBot⚡, a GenAI assistant for telecom-energy. "
                "Keep a professional, informative tone.\n"
            )

            # Have to append background prompt from qdrant and remove system prompt
            prompt = f"{system_prompt}User: {user_query}\nAssistant:"
            
            # Call Gemini directly with async function - no need for to_thread
            response = await call_gemini(
                prompt, 
                max_tokens=max_tokens, 
                temperature=temperature
            )
            
            # Return a Python dictionary, not a JSON string
            yield {
                "type": "text",
                "content": response
            }
        except Exception as e:
            print(f"Error in Chat service: {str(e)}")
            yield {
                "type": "text",
                "content": f"I'm sorry, I encountered an error while processing your request. Please try again later."
            }


