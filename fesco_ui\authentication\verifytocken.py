from authentication.user import User
from helpers.pg import database
import os
from flask import Flask, jsonify

def verify_token(token,userid):
    try:
        if token.startswith("Bearer "):
            token   =   token[7:]
        decoded_token   =   User().verify_jwt(token)
        user_query      =   f"SELECT email FROM users WHERE id = {decoded_token['id']}"
        user_data       =   database(os.getcwd()).read_sql_query(user_query)
        if not user_data or not user_data[0]:
            return {"error": "Invalid user ID in token."}
        return {"message": "Token is valid." , 'userid':userid}
    
    except KeyError:
        return {"error": "Invalid token structure."}
        
    except Exception as e:
        return {"error": "Invalid or expired token."}