from sentence_transformers import SentenceTransformer
from qdrant_client.models import <PERSON>lter, FieldCondition, MatchValue, ScrollRequest
from utils.qdrant_connection import client
from utils.config import DEFAULT_SCHEMA_COLUMNS, DEFAULT_SIMILAR_QUERIES, DEFAULT_ROUTER_EXAMPLES
import asyncio
import re

class QdrantService:
    """Service for interacting with Qdrant vector database"""
    
    def __init__(self):
        self.encoder = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")
        self.embed_model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")
        # self.collection_name = "knowledge_documents"
        self.collection_name = "fesco_knowledge" # changed remember to undo
        self.schema_collection = "schema_embeddings"
    
    async def get_text_by_type(self, metadata_type: str, query: str = None, limit: int = 3) -> str:
        """
        Retrieves relevant text based on metadata type and query using semantic search.
        
        Args:
            metadata_type (str): Type of metadata to retrieve (e.g., 'knowledge_base', 'fesco_instructions')
            query (str): User's query for semantic search
            limit (int): Maximum number of results to return
        """
        # Use the query for semantic search if provided, otherwise use metadata_type
        search_text = query # if query else metadata_type
        
        # Run encoding in a thread pool since it's CPU-bound
        search_vector = await asyncio.to_thread(self.encoder.encode, search_text)
        search_vector = search_vector.tolist()
        
        # Run Qdrant search in a thread pool since it's I/O-bound and not async
        results = await asyncio.to_thread(
            client.search,
            collection_name=self.collection_name,
            query_vector=search_vector,
            query_filter={
                "must": [
                    {"key": "type", "match": {"value": metadata_type}}
                ]
            },
            limit=limit,
            with_payload=True
        )
        
        # Extract and combine relevant texts
        texts = [r.payload.get("text", "") for r in results if r.payload.get("text")]
        return "\n\n".join(texts) if texts else ""

    async def retrieve_system_prompt(self, collection_name: str = "knowledge_documents") -> str:
        # Run encoding in a thread pool
        dummy_vector = await asyncio.to_thread(self.encoder.encode, "instructions")
        dummy_vector = dummy_vector.tolist()

        # Run Qdrant search in a thread pool
        search_result = await asyncio.to_thread(
            client.search,
            collection_name=collection_name,
            query_vector=dummy_vector,
            limit=1,
            query_filter={
                "must": [
                    {"key": "metadata_type", "match": {"value": "system_prompt"}}
                ]
            }
        )

        if search_result:
            return search_result[0].payload.get("content", "")
        else:
            return ""
    
    async def retrieve_knowledge(self, query, top_k=5):
        print("Connected to collection:", self.collection_name)
        
        # Run encoding in a thread pool
        query_embedding = await asyncio.to_thread(self.embed_model.encode, query)
        query_embedding = query_embedding.tolist()

        # Run Qdrant search in a thread pool
        search_results = await asyncio.to_thread(
            client.search,
            collection_name=self.collection_name,
            query_vector=query_embedding,
            limit=top_k,
            with_payload=True
        )

        print("Search results:")
        for hit in search_results:
            print(hit.payload)

        # Extract matched content from payload
        matches = []
        for hit in search_results:
            payload = hit.payload
            content = payload.get("content") or payload.get("text") or str(payload)
            matches.append(content)

        return matches
    
    async def preprocess_query(self, query: str) -> str:
        """
        Preprocesses the user query to improve matching with schema columns.
        
        Args:
            query (str): The original user query
        
        Returns:
            str: Processed query
        """
        # Convert to lowercase
        processed = query.lower()
        
        # Remove apostrophes first (for contractions like "what's" -> "whats")
        processed = processed.replace("'", "")
        
        # Remove special characters except spaces
        processed = re.sub(r'[^\w\s]', ' ', processed)
        
        # Remove extra whitespace
        processed = re.sub(r'\s+', ' ', processed).strip()
        
        return processed
    
    async def get_relevant_schema_columns(self, query: str, limit: int = None) -> list:
        """
        Retrieves relevant database schema columns based on semantic similarity to the query.
        
        Args:
            query (str): User's natural language query
            limit (int): Maximum number of relevant columns to return (default from config)
            
        Returns:
            list: List of formatted column information
        """
        # Use the default from config if limit is not specified
        if limit is None:
            limit = DEFAULT_SCHEMA_COLUMNS
            
        # Preprocess the query
        processed_query = await self.preprocess_query(query)
        
        # Generate embedding for the query
        query_vector = await asyncio.to_thread(self.encoder.encode, processed_query)
        query_vector = query_vector.tolist()
        
        # Search for relevant columns in schema_embeddings collection
        search_results = await asyncio.to_thread(
            client.search,
            collection_name=self.schema_collection,
            query_vector=query_vector,
            limit=limit,
            with_payload=True,
            with_vectors=False
        )
        
        # Format the results
        formatted_columns = await self.format_schema_results(search_results)
        
        return formatted_columns
    
    async def format_schema_results(self, search_results: list) -> list:
        """
        Formats the schema search results into a consistent structure.
        
        Args:
            search_results (list): Raw search results from Qdrant
            
        Returns:
            list: Formatted column information
        """
        formatted_results = []
        
        for result in search_results:
            payload = result.payload
            
            # Extract fields with null handling
            table = payload.get("table", "unknown")
            column = payload.get("column", "unknown")
            dtype = payload.get("dtype", "unknown")
            column_description = payload.get("column_description", "")
            
            # Format as "table: column: datatype: description"
            formatted_info = f"{table}: {column}: {dtype}"
            
            # Add description if available
            if column_description:
                formatted_info += f": {column_description}"
            
            formatted_results.append({
                "formatted": formatted_info,
                "table": table,
                "column": column,
                "dtype": dtype,
                "description": column_description,
                "score": result.score
            })
        
        return formatted_results

    async def retrieve_similar_queries(self, user_query: str, top_k: int = None) -> list:
        """
        Retrieves semantically similar pre-made queries based on user's natural language input.
        
        Args:
            user_query (str): User's natural language query
            top_k (int): Maximum number of similar queries to return, defaults to DEFAULT_SIMILAR_QUERIES
        
        Returns:
            list: List of dictionaries containing natural_language, sql_query, and description
        """
        # Use the default from config if top_k is not specified
        if top_k is None:
            top_k = DEFAULT_SIMILAR_QUERIES
        
        # Generate embedding for the user query
        query_vector = await asyncio.to_thread(self.encoder.encode, user_query)
        query_vector = query_vector.tolist()
        
        # Search for similar queries in the query_embeddings collection
        search_results = await asyncio.to_thread(
            client.search,
            collection_name="query_embeddings",
            query_vector=query_vector,
            limit=top_k,
            with_payload=True,
            with_vectors=False
        )
        
        # Extract the relevant fields from each result
        similar_queries = []
        for hit in search_results:
            payload = hit.payload
            similar_queries.append({
                "natural_language": payload.get("natural_language", ""),
                "sql_query": payload.get("sql_query", ""),
                "description": payload.get("description", ""),
                "score": hit.score
            })
        
        return similar_queries

    async def get_query_instructions(self, instruction_type: str = None) -> list:
        """
        Retrieves query instructions from the query_instructions collection.
        
        Args:
            instruction_type (str, optional): Type of instruction to retrieve (e.g., 'instructions', 'sql_guidelines')
                                             If None, returns all instructions.
        
        Returns:
            list: List of instruction documents with their text and type
        """
        # Create a scroll request with filter if instruction_type is provided
        scroll_request = ScrollRequest(limit=100)
        
        # Run Qdrant scroll in a thread pool since it's I/O-bound
        if instruction_type:
            # Use search with filter instead of scroll with filter
            query_filter = Filter(
                must=[
                    FieldCondition(
                        key="type",
                        match=MatchValue(value=instruction_type)
                    )
                ]
            )
            
            # Use search instead of scroll for filtering
            search_results = await asyncio.to_thread(
                client.search,
                collection_name="query_instructions",
                query_vector=[0.0] * 384,  # Dummy vector (adjust dimension as needed)
                query_filter=query_filter,
                limit=100,
                with_payload=True
            )
            
            # Extract the relevant information from each point
            instructions = []
            for point in search_results:
                if point.payload:
                    instructions.append({
                        "id": point.id,
                        "type": point.payload.get("type", ""),
                        "text": point.payload.get("text", "")
                    })
        else:
            # Use scroll without filter to get all instructions
            scroll_result = await asyncio.to_thread(
                client.scroll,
                collection_name="query_instructions",
                limit=100,
                with_payload=True
            )
            
            # Extract the relevant information from each point
            instructions = []
            for point in scroll_result[0]:  # scroll returns (points, next_page_offset)
                if point.payload:
                    instructions.append({
                        "id": point.id,
                        "type": point.payload.get("type", ""),
                        "text": point.payload.get("text", "")
                    })
        
        return instructions

    async def retrieve_similar_router_examples(self, user_query: str, top_k: int = None) -> list:
        """
        Retrieves semantically similar router examples based on user's query.
        
        Args:
            user_query (str): User's natural language query
            top_k (int): Maximum number of similar examples to return, defaults to DEFAULT_ROUTER_EXAMPLES
        
        Returns:
            list: List of dictionaries containing example queries and their classifications
        """
        # Use the default from config if top_k is not specified
        if top_k is None:
            top_k = DEFAULT_ROUTER_EXAMPLES
        
        # Generate embedding for the user query
        query_vector = await asyncio.to_thread(self.encoder.encode, user_query)
        query_vector = query_vector.tolist()
        
        # Search for similar router examples in the router_embeddings collection
        search_results = await asyncio.to_thread(
            client.search,
            collection_name="router_embeddings",
            query_vector=query_vector,
            limit=top_k,
            with_payload=True,
            with_vectors=False
        )
        
        # Extract the relevant fields from each result
        similar_examples = []
        for hit in search_results:
            payload = hit.payload
            similar_examples.append({
                "user_query": payload.get("user_query", ""),
                "response_full_text": payload.get("response_full_text", ""),
                "score": payload.get("score", 0),
                "requires_db": payload.get("requires_db", "NO"),
                "similarity_score": hit.score
            })
        
        return similar_examples

    async def fetch_router_instructions(self) -> str:
        """
        Retrieves router instructions from the router_instructions collection.
        No semantic search is needed, just fetching by type.
        
        Returns:
            str: The text content of the router instructions
        """
        # Create a filter for documents with type "router_instructions"
        query_filter = Filter(
            must=[
                FieldCondition(
                    key="type",
                    match=MatchValue(value="router_instructions")
                )
            ]
        )
        
        # Use search with filter and a dummy vector
        # The vector doesn't matter since we're using an exact filter match
        search_results = await asyncio.to_thread(
            client.search,
            collection_name="router_instructions",
            query_vector=[0.0] * 384,  # Dummy vector (adjust dimension as needed)
            query_filter=query_filter,
            limit=1,  # We only need one document
            with_payload=True
        )
        
        # Extract the text from the result
        if search_results and len(search_results) > 0:
            return search_results[0].payload.get("text", "")
        else:
            # Return empty string if no instructions found
            return ""
