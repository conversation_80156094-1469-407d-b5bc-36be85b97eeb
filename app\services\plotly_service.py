import os
import pandas as pd
from services.qdrant_service import QdrantService
from utils.llm_utils import call_gemini 
import plotly.express as px
import plotly.graph_objects as go
from pathlib import Path
from utils.config import OUTPUT_DIR, MAX_RETRIES
import asyncio

class PlotlyService:
    """Service for generating data visualizations using Plotly"""
    
    def __init__(self):
        self.qdrant_service = QdrantService()
    
    def validate_code_syntax(self, code: str) -> bool:
        """
        Validates if the code has proper syntax.
        
        Args:
            code (str): Code to validate
            
        Returns:
            bool: True if syntax is valid, False otherwise
        """
        try:
            compile(code, '<string>', 'exec')
            return True
        except SyntaxError:
            return False

    def clean_visualization_code(self, code: str) -> str:
        """
        Cleans and validates the visualization code.
        """
        # Remove any markdown formatting if present
        if "```python" in code:
            code = code.split("```python")[1].split("```")[0].strip()
        elif "```" in code:
            code = code.split("```")[1].split("```")[0].strip()
        
        # Basic syntax fixes
        code = code.replace('\r', '\n')  # Normalize line endings
        
        # Ensure all parentheses are closed
        open_count = code.count('(')
        close_count = code.count(')')
        if open_count > close_count:
            code += ')' * (open_count - close_count)
        
        return code

    async def generate_visualization(self, sql_query: str, user_question: str, file_name: str, working_directory: str) -> str:
        """
        Generates visualization code based on SQL query results.
        """
        # Use the provided working_directory but ensure it exists
        working_dir = Path(working_directory)
        working_dir.mkdir(parents=True, exist_ok=True)
        
        csv_path = working_dir / f"{file_name}.csv"
        json_path = working_dir / f"{file_name}.json"
        dataframe_location = str(csv_path)
        
        # Read CSV in a thread pool since it's I/O-bound
        df = await asyncio.to_thread(pd.read_csv, csv_path)
        df = df.loc[:, ~df.columns.str.contains('^Unnamed')]
        
        # Gather DataFrame metadata for the prompt
        num_rows = len(df)
        data_types = df.dtypes.to_dict()
        summary_stats = df.describe(include='all').to_dict()
        sample_data = df.head(5).to_dict(orient='records')
        columns = df.columns.tolist()
        
        # A robust, step-by-step prompt for Gemini
        prompt_base = f"""
            You are a data visualization expert. Given the results of a SQL query and the user's question, generate Python code using Plotly (Express or Graph Objects) that:

            1. Reads the CSV located at:
            `{dataframe_location}`  
            2. Drops any rows containing nulls in any column.  
            3. Analyzes the DataFrame :  
            • SQL Query: `{sql_query}`  
            • User Question: `{user_question}`  
            4. First priority let user decides what type of graph to plot else you decide what type of graph to plot.
            DataFrame details: 
            - **Columns**: {columns}  
            - **Number of rows**: {num_rows}  
            - **Data types**: {data_types}  
            - **Summary statistics**: {summary_stats}  
            - **Example rows**: {sample_data}  

            Requirements for the generated code:  
            • Use `plotly_dark` template.  
            • Include axis labels, titles reflecting the user’s intent, and legends when needed.  
            • Read the data from `{dataframe_location}`.  
            • Drop nulls before plotting !important.
            • Serialize the resulting figure to JSON via `fig.to_json()` and write it to `{json_path}`.  
            • Return only valid, executable Python code (no markdown fences, no comments).
            • When you call fig.to_json(), write that text directly into the output file. Do not wrap it in Python quotes, do not escape   characters, and do not return any markdown or code fences—just the raw JSON.

            Provide the full code snippet.
            """
        
        try:
            raw_code = await call_gemini(prompt_base)
            cleaned_code = self.clean_visualization_code(raw_code)
            if self.validate_code_syntax(cleaned_code):
                return cleaned_code
            else:
                raise ValueError("Invalid code syntax")
        except Exception as e:
            print(f"Error generating visualization code: {e}")
            raise ValueError(f"Failed to generate visualization: {str(e)}")

    async def generate_visualization_with_retry(self, sql_query: str, user_question: str, file_name: str, working_directory: str) -> tuple[bool, str]:
        """
        Generates visualization code with retry mechanism.
        
        Args:
            sql_query (str): The SQL query that was executed
            user_question (str): The original user question
            file_name (str): Base filename for the visualization
            working_directory (str): Directory to save visualization files
        
        Returns:
            tuple: (success: bool, result: str) - Either (True, code) or (False, error_message)
        """
        retry_count = 0
        last_error = None
        last_code = None
        
        while retry_count <= MAX_RETRIES:
            try:
                code = await self.generate_visualization(sql_query, user_question, file_name, working_directory)
                
                # Try to execute the code
                local_namespace = {}
                exec(code, globals(), local_namespace)
                
                # Check if the JSON file was created
                json_path = Path(working_directory) / f"{file_name}.json"
                if not json_path.exists():
                    if retry_count < MAX_RETRIES:
                        last_error = "Code executed but did not generate JSON output file"
                        last_code = code
                        retry_count += 1
                        continue
                    else:
                        return False, "Visualization code executed but did not generate output file"
                
                # Success!
                return True, code
                
            except Exception as e:
                error_message = str(e)
                print(f"Error in visualization attempt {retry_count+1}: {error_message}")
                
                if retry_count < MAX_RETRIES:
                    last_error = error_message
                    last_code = code if 'code' in locals() else None
                    retry_count += 1
                    continue
                else:
                    return False, f"Failed to generate visualization after {MAX_RETRIES+1} attempts. Error: {error_message}"

    async def generate_visualization_with_error(self, sql_query: str, user_question: str, file_name: str, 
                                         working_directory: str, error_msg: str, previous_code: str = None,
                                         retry_count: int = 0) -> str:
        """
        Generates visualization code with error context from previous attempts.
        
        Args:
            sql_query (str): The SQL query that was executed
            user_question (str): The original user question
            file_name (str): Base filename for the visualization
            working_directory (str): Directory to save visualization files
            error_msg (str): Error message from previous attempt
            previous_code (str): The code that failed in the previous attempt
            retry_count (int): Current retry count
        
        Returns:
            str: Generated visualization code
        """
        # Use the provided working_directory but ensure it exists
        working_dir = Path(working_directory)
        working_dir.mkdir(parents=True, exist_ok=True)
        
        csv_path = working_dir / f"{file_name}.csv"
        json_path = working_dir / f"{file_name}.json"
        dataframe_location = str(csv_path)
        
        df = await asyncio.to_thread(pd.read_csv, csv_path)
        df = df.loc[:, ~df.columns.str.contains('^Unnamed')]
        
        # Gather DataFrame metadata for the prompt
        num_rows = len(df)
        data_types = df.dtypes.to_dict()
        summary_stats = df.describe(include='all').to_dict()
        sample_data = df.head(5).to_dict(orient='records')
        columns = df.columns.tolist()
        
        # A robust, step-by-step prompt for Gemini with error context
        prompt_base = f"""
            You are a data visualization expert. Given the results of a SQL query and the user's question, generate Python code using Plotly (Express or Graph Objects) that:

            1. Reads the CSV located at:
            `{dataframe_location}`  
            2. Drops any rows containing nulls in any column.  
            3. Analyzes the DataFrame :  
            • SQL Query: `{sql_query}`  
            • User Question: `{user_question}`  
            4. First priority let user decides what type of graph to plot else you decide what type of graph to plot.
            DataFrame details: 
            - **Columns**: {columns}  
            - **Number of rows**: {num_rows}  
            - **Data types**: {data_types}  
            - **Summary statistics**: {summary_stats}  
            - **Example rows**: {sample_data}  

            Requirements for the generated code:  
            • Use `plotly_dark` template.  
            • Include axis labels, titles reflecting the user’s intent, and legends when needed.  
            • Read the data from `{dataframe_location}`.  
            • Drop nulls before plotting.  
            • Serialize the resulting figure to JSON via `fig.to_json()` and write it to `{json_path}`.  
            • Return only valid, executable Python code (no markdown fences, no comments).
            • When you call fig.to_json(), write that text directly into the output file. Do not wrap it in Python quotes, do not escape characters, and do not return any markdown or code fences—just the raw JSON.

            Provide the full code snippet.
            """

        # Add error context for retries
        if error_msg and previous_code:
            prompt_base += f"""
            IMPORTANT: Previous visualization code failed with error: {error_msg}
            This is retry attempt {retry_count} of {MAX_RETRIES}.
            
            Here is the previous code that failed:
            ```python
            {previous_code}
            ```
            
            Please fix the issues in the code and ensure it works correctly.
            """
        
        prompt_base += "\nProvide the full code snippet."

        print(f"Generating visualization with error context (Attempt {retry_count})")
        
        try:
            raw_code = await call_gemini(prompt_base)
            cleaned_code = self.clean_visualization_code(raw_code)
            if self.validate_code_syntax(cleaned_code):
                return cleaned_code
            else:
                raise ValueError("Invalid code syntax")
        except Exception as e:
            print(f"Error generating visualization code with error context: {e}")
            raise ValueError(f"Failed to generate visualization: {str(e)}")

    async def generate_fesco_visualization(self, user_question: str, context_block: str, file_name: str, working_directory: str) -> str:
        """
        Generates visualization code for FESCO data based on user question.
        
        Args:
            user_question (str): The original user question
            file_name (str): Base filename for the visualization
            working_directory (str): Directory to save visualization files
        
        Returns:
            str: Generated visualization code
        """
        # Use the provided working_directory but ensure it exists
        working_dir = Path(working_directory)
        working_dir.mkdir(parents=True, exist_ok=True)
        
        csv_path = working_dir / f"{file_name}.csv"
        json_path = working_dir / f"{file_name}.json"
        dataframe_location = str(csv_path)
        
        # Read CSV in a thread pool since it's I/O-bound
        df = await asyncio.to_thread(pd.read_csv, csv_path)
        df = df.loc[:, ~df.columns.str.contains('^Unnamed')]
        
        # Gather DataFrame metadata for the prompt
        num_rows = len(df)
        data_types = df.dtypes.to_dict()
        summary_stats = df.describe(include='all').to_dict()
        sample_data = df.head(5).to_dict(orient='records')
        columns = df.columns.tolist()

        fesco_visualization_prompt = await self.qdrant_service.get_text_by_type(
            "fesco_visualization_prompt",
            "", 
            limit=1
        )
        
        prompt_base = f"""
            Previous Context:
            {context_block}
            {fesco_visualization_prompt}

            1. Reads the CSV located at:
            `{dataframe_location}`  
            2. Drops any rows containing nulls in any column.  
            3. Analyzes the DataFrame based on:  
            • User Question: `{user_question}`   
            - **Columns**: {columns}  
            - **Number of rows**: {num_rows}  
            - **Data types**: {data_types}  
            - **Summary statistics**: {summary_stats}  
            - **Example rows**: {sample_data}  

            • Read the data from `{dataframe_location}`.  
            • Serialize the resulting figure to JSON via `fig.to_json()` and write it to `{json_path}`.  
            • Return only valid, executable Python code (no markdown fences, no comments).
            • When you call fig.to_json(), write that text directly into the output file. Do not wrap it in Python quotes, do not escape characters, and do not return any markdown or code fences—just the raw JSON.

            Provide the full code snippet.
            """
        
        try:
            raw_code = await call_gemini(prompt_base)
            cleaned_code = self.clean_visualization_code(raw_code)
            print(f"Cleaned code: {cleaned_code}")
            if self.validate_code_syntax(cleaned_code):
                return cleaned_code
            else:
                raise ValueError("Invalid code syntax")
        except Exception as e:
            print(f"Error generating FESCO visualization code: {e}")
            raise ValueError(f"Failed to generate FESCO visualization: {str(e)}")

    async def generate_fesco_visualization_with_retry(self, user_question: str, context_block: str ,file_name: str, working_directory: str) -> tuple[bool, str]:
        """
        Generates FESCO visualization code with retry mechanism.
        
        Args:
            user_question (str): The original user question
            file_name (str): Base filename for the visualization
            working_directory (str): Directory to save visualization files
        
        Returns:
            tuple: (success: bool, result: str) - Either (True, code) or (False, error_message)
        """
        retry_count = 0
        last_error = None
        last_code = None
        
        while retry_count <= MAX_RETRIES:
            try:
                code = await self.generate_fesco_visualization(user_question, context_block,file_name, working_directory)
                
                # Try to execute the code
                local_namespace = {}
                exec(code, globals(), local_namespace)
                
                # Check if the JSON file was created
                json_path = Path(working_directory) / f"{file_name}.json"
                if not json_path.exists():
                    if retry_count < MAX_RETRIES:
                        last_error = "Code executed but did not generate JSON output file"
                        last_code = code
                        retry_count += 1
                        continue
                    else:
                        return False, "Visualization code executed but did not generate output file"
                
                # Success!
                return True, code
                
            except Exception as e:
                error_message = str(e)
                print(f"Error in FESCO visualization attempt {retry_count+1}: {error_message}")
                
                if retry_count < MAX_RETRIES:
                    last_error = error_message
                    last_code = code if 'code' in locals() else None
                    retry_count += 1
                    continue
                else:
                    # Return a generic error message instead of the technical details
                    return False, "Visualization could not be generated after multiple attempts"

    async def generate_fesco_visualization_with_error(self, user_question: str, file_name: str, 
                                     working_directory: str, error_msg: str, previous_code: str = None,
                                     retry_count: int = 0) -> str:
        """
        Generates FESCO visualization code with error context from previous attempts.
        
        Args:
            user_question (str): The original user question
            file_name (str): Base filename for the visualization
            working_directory (str): Directory to save visualization files
            error_msg (str): Error message from previous attempt
            previous_code (str): The code that failed in the previous attempt
            retry_count (int): Current retry count
        
        Returns:
            str: Generated visualization code
        """
        # Use the provided working_directory but ensure it exists
        working_dir = Path(working_directory)
        working_dir.mkdir(parents=True, exist_ok=True)
        
        csv_path = working_dir / f"{file_name}.csv"
        json_path = working_dir / f"{file_name}.json"
        dataframe_location = str(csv_path)
        
        df = await asyncio.to_thread(pd.read_csv, csv_path)
        df = df.loc[:, ~df.columns.str.contains('^Unnamed')]
        
        # Gather DataFrame metadata for the prompt
        num_rows = len(df)
        data_types = df.dtypes.to_dict()
        summary_stats = df.describe(include='all').to_dict()
        sample_data = df.head(5).to_dict(orient='records')
        columns = df.columns.tolist()

        fesco_visualization_prompt = await self.qdrant_service.get_text_by_type(
            "fesco_visualization_prompt",
            "", 
            limit=1
        )
        
        prompt_base = f"""
           
            {fesco_visualization_prompt}

            1. Reads the CSV located at:
            `{dataframe_location}`  
            2. Drops any rows containing nulls in any column.  
            3. Analyzes the DataFrame based on:  
            • User Question: `{user_question}`   
            - **Columns**: {columns}  
            - **Number of rows**: {num_rows}  
            - **Data types**: {data_types}  
            - **Summary statistics**: {summary_stats}  
            - **Example rows**: {sample_data}  

            • Read the data from `{dataframe_location}`.  
            • Serialize the resulting figure to JSON via `fig.to_json()` and write it to `{json_path}`.  
            • Return only valid, executable Python code (no markdown fences, no comments).
            • When you call fig.to_json(), write that text directly into the output file. Do not wrap it in Python quotes, do not escape characters, and do not return any markdown or code fences—just the raw JSON.

            Provide the full code snippet.
            """

        # Add error context for retries
        if error_msg and previous_code:
            prompt_base += f"""
            IMPORTANT: Previous visualization code failed with error: {error_msg}
            This is retry attempt {retry_count} of {MAX_RETRIES}.
            
            Here is the previous code that failed:
            ```python
            {previous_code}
            ```
            
            Please fix the issues in the code and ensure it works correctly.
            """
        
        prompt_base += "\nProvide the full code snippet."

        print(f"Generating FESCO visualization with error context (Attempt {retry_count})")
        
        try:
            raw_code = await call_gemini(prompt_base)
            cleaned_code = self.clean_visualization_code(raw_code)
            if self.validate_code_syntax(cleaned_code):
                return cleaned_code
            else:
                raise ValueError("Invalid code syntax")
        except Exception as e:
            print(f"Error generating FESCO visualization code with error context: {e}")
            raise ValueError(f"Failed to generate FESCO visualization: {str(e)}")
