import os
import google.generativeai as genai

# Base directory - use environment variable or default to app directory
BASE_DIR = os.environ.get('APP_BASE_DIR', os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Output directory for SQL results and visualizations
OUTPUT_DIR = os.environ.get('APP_OUTPUT_DIR', os.path.join(BASE_DIR, 'output', 'sql_results'))

# Qdrant Configuration
QDRANT_HOST = os.environ.get('QDRANT_HOST', "https://10201edb-bbe0-4211-98a5-b927f96b4852.us-east4-0.gcp.cloud.qdrant.io")
QDRANT_API_KEY = os.environ.get('QDRANT_API_KEY', "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.rc2xZyUVitzfnrfc9p-_3oVkk6A6g_X8s9TC3vMvu7k")

# Database Configuration
DB_HOST = os.environ.get('DB_HOST', "**************")
DB_PORT = os.environ.get('DB_PORT', "5432")
DB_NAME = os.environ.get('DB_NAME', "mytestdb")
DB_USER = os.environ.get('DB_USER', "khawar")
DB_PASS = os.environ.get('DB_PASS', "SoftooSCT")

API_KEYS = [
    "AIzaSyCllMOP7Kxb0vwbNDjylzXhSxmohUjEUow",
    "AIzaSyBFh0apHZcHx9ni9zxFU9VBiFmVf_3iU3Q",
    "AIzaSyCJ7IiGyHWpTuh6rXg1YOexvGD4y7X80ec",
    "AIzaSyCMv-OuqfxO8Wq0v1ZN0ZgL9aQe2p3MM5k",
    "AIzaSyA2Xxyb2bGJhI9Kh-KxKzYIslhGRWTcMc4",
    "AIzaSyDTel3hGaXhchXthTdXjFkDy-hs26JCTbI",
    "AIzaSyDxDjSfDPCfJaKv-ZFa9AP59l2QsXX3KV0",
    "AIzaSyCy4g7I5CvbtBmumjvtnfaWdFhDhICEcKU",
    "AIzaSyAz50RwLyct3sBvecc5Mak9ps5bFsrtcok",
    "AIzaSyD3J-BufebJmdQ6STeEKJjqpEIN4M_JA9g",
    "AIzaSyCPDDfVRaO4628QSipLLq3fgjnxusSWuCs",
    "AIzaSyCrHkj0Wv9FwF-kvSuaL4j2PYicrjy12Fc",
    "AIzaSyB0hzFbRtvm5M8_aXKQGcmHAXHjRKZ_VEA",
    "AIzaSyDUmjWzConQ21BHHKBMtdzspw4n4T-yHXQ",
    "AIzaSyCi7rimWp1rTJrjcNIas2jjlknqfhBArPY",
    "AIzaSyBrbwKrOCgd1gbaDVwRo11M5ne2Qb98rCA",
    "AIzaSyAdVQWpNAIlhysRVcJR4p4K1NoQI0HOdss",
    "AIzaSyC9XehVDHb8ImL2pJtilpCbLflXV9jbmlw",
    "AIzaSyB9sm_D3gpfZkyckd79NU22J5qsltkhiSI",
    "AIzaSyC281AOUgZME-2ueK6qFfA-sOP3EHim9O0",
    "AIzaSyATh8PlDepzfGSIfocbu_3Y5eDU5gs1y7w",
]

# Default configuration
DEFAULT_MODEL_NAME = "gemini-2.0-flash"
DEFAULT_MULTI_AUTH = 1
DEFAULT_PARAGRAPHS = 1
DEFAULT_SCHEMA_COLUMNS = 5  # Default number of schema columns to retrieve
DEFAULT_SIMILAR_QUERIES = 5  # Default number of similar queries to retrieve
DEFAULT_ROUTER_EXAMPLES = 15  # Default number of similar router examples to retrieve

# Number of retries for model API calls
MAX_RETRIES = 3

# CONVERSATIONAL_KEYWORDS = [
#     'tell me about', 'how are you', "what's up", 'how was your day',
#     'hi', 'hello', 'hey', 'greetings', 'nice to meet you',
#     'who are you', 'what can you do', 'introduce yourself',
#     'can you help me', 'help', 'assist me'
# ]

CRUD_KEYWORDS = [
    'insert', 'create', 'add', 'put', 'upload',
    'update', 'modify', 'change', 'alter', 'edit',
    'delete', 'remove', 'drop', 'truncate',
    'replace', 'write', 'save'
]
