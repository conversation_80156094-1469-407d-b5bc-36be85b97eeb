import datetime
from typing import <PERSON>ple
import uuid
import re
import pandas as pd
from utils.qdrant_connection import client 
from qdrant_client.http.models import Distance, VectorParams

def generate_csv_filename():
    """
    Generates a unique filename for CSV exports using timestamp and UUID.
    
    Returns:
        str: A filename in the format 'query_result_YYYYMMDDHHMMSS_UNIQUEID'
    """
    current_time = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    unique_id = uuid.uuid4().hex[:8]  # Generate a short unique ID
    return f"query_result_{current_time}_{unique_id}"


def get_dataframe_info(df: pd.DataFrame) -> Tuple[int, str, str, str]:
    """
    Analyzes a DataFrame and returns detailed information about its structure and content.
    
    Args:
        df (pd.DataFrame): The DataFrame to analyze
        
    Returns:
        Tuple containing:
        - int: Number of rows
        - str: Data types of columns
        - str: Summary statistics
        - str: Sample data (first 5 rows)
    """
    # Get number of rows
    num_rows = len(df)
    
    # Get data types
    data_types = df.dtypes.to_dict()
    data_types_str = "; ".join([f"{col}: {dtype}" for col, dtype in data_types.items()])
    
    # Get summary statistics
    summary_stats = df.describe(include='all').to_dict()
    summary_stats_str = "; ".join([
        f"{col}: " + ", ".join([
            f"{stat}={value:.2f}" if isinstance(value, float) 
            else f"{stat}={value}" 
            for stat, value in col_stats.items() 
            if stat != 'count'
        ]) for col, col_stats in summary_stats.items()
    ])
    
    # Get sample data
    sample_data = df.head(5).to_csv(index=False, sep=',')
    
    return num_rows, data_types_str, summary_stats_str, sample_data


def clean_sentence(text: str) -> str:
    """
    Cleans and normalizes input text by removing special characters and extra whitespace.
    
    Args:
        text (str): Input text to clean
        
    Returns:
        str: Cleaned text
    """
    # Remove special characters but keep asterisks
    text = re.sub(r'[^a-zA-Z0-9\s\*]', '', text)
    # Normalize whitespace
    text = ' '.join(text.split())
    return text

def clean_output_response(response: str) -> str:
    """
    Cleans all routing/scoring artifacts from final output responses.
    Only used for user-facing output cleaning.
    """
    if not response:
        return response
        
    lines = response.split('\n')
    cleaned_lines = []
    skip_line = False
    
    for line in lines:
        # Skip lines containing routing artifacts
        if any(artifact in line for artifact in ['Score:', 'Requires DB:', 'Required DB:']):
            skip_line = True
            continue
        # If we hit an empty line after artifacts, skip it too
        if skip_line and not line.strip():
            skip_line = False
            continue
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines).strip()



