import jwt
from jwt import InvalidTokenError
from authentication.crypto import CryptoService
import base64
import time

class User:
    def __init__(self):
        self.secret_key = "aa886ea7670cc197f730591489db19f49ef924bfb7a2e733e2ec5419a6337d64"
        self.crypto_service = CryptoService()

    def verify_jwt(self, encrypted_token):
        try:
            decrypted_token = self.crypto_service.decrypt(encrypted_token)
            print(decrypted_token)
            # decrypted_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjQxMiIsInJvbGVJZCI6IjIwIiwiaWF0IjoxNzM1MjA4NzA2LCJleHAiOjE3MzUyOTUxMDZ9.yOvzUubK-CNCceH6JCVqlu2pBCSgK8Ts51mrS5khb6A"
            if not decrypted_token:
                raise ValueError("Decryption failed.")

            # Verify and decode the token
            decoded_payload = jwt.decode(
                decrypted_token,
                self.secret_key,
                algorithms=["HS256"],
                options={"verify_signature": True}
            )
            return decoded_payload
        except InvalidTokenError as e:
            raise ValueError(f"Invalid JWT token: {e}")
        except Exception as e:
            raise ValueError(f"{e}")

if __name__ == "__main__":
    # Example encrypted token (replace with a valid encrypted JWT)
    encrypted_token = "90a5c12243bc3ddfa0fa5bdb654f8c99137774d30b7bb56d7bb3be33d9b82882d26e7b145071b23d933a43a96ed216ab606fbd9e82f49d4dc0406a7a646d9234744fe892d3ca49d37b701f92bcda093b1c58b16715024544e821d49dd694e875cc0fd52d122c1eead97fa07276c800a3ea65ff96e56c0ee91a48dd1f2cec5801aac4db7f519a12ec025e52c15079ff6662ab93e19ba86c98a9380d4e3ac68b3e3d13ea8154f6c4be4a088a682569e64f"

    try:
        payload = User().verify_jwt(encrypted_token)
        print("Token is valid. Payload:", payload)
    except ValueError as e:
        print(e)
