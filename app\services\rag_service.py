import asyncio
from utils.llm_utils import call_gemini
from utils.utils import clean_output_response
from services.qdrant_service import QdrantService
import re

class RagService:
    """Service for RAG (Retrieval Augmented Generation) processing"""
    
    def __init__(self):
        self.qdrant_service = QdrantService()
    
    async def generate_rag_response(self, user_query: str, max_tokens: int = 512, temperature: float = 0.7, invalid_schema_msg: str = None):
        """
        Asynchronously retrieves background + instruction docs, then calls Gemini once.
        Now handles cases where SQL validation failed.
        """
        print("\n=== RAG Processing ===")
        print(f"Query: {user_query}")
        
        # Get both background and schema_linker documents asynchronously
        background_text = await self.qdrant_service.get_text_by_type(
            "background", 
            user_query, 
            limit=3
        )
        
        schema_linker_text = await self.qdrant_service.get_text_by_type(
            "schema_linker", 
            user_query, 
            limit=2
        )
        
        # Get instruction text asynchronously
        instruction_text = await self.qdrant_service.get_text_by_type(
            "instructions", 
            "", 
            limit=1
        )
        
        # Build the prompt
        prompt_parts = []
        
        # Add system prompt
        # prompt_parts.append(
        #     "You are ThunderBot⚡, a GenAI assistant for telecom-energy. "
        #     "Keep a professional, informative tone.\n"
        # )
        
        # Add background information if available
        if background_text:
            prompt_parts.append(f"### Background Information:\n{background_text}")
        
        # Add schema information if available
        if schema_linker_text:
            prompt_parts.append(f"### Database Schema Information:\n{schema_linker_text}")
        
        # Add instructions if available
        if instruction_text:
            prompt_parts.append(f"### Instructions:\n{instruction_text}")
        
        # Add invalid schema message if provided
        if invalid_schema_msg:
            prompt_parts.append(
                f"### Important Note:\n{invalid_schema_msg}\n\n"
                f"Please provide a helpful response based on the available information, "
                f"without attempting to execute SQL queries."
            )
        
        # Add user query
        prompt_parts.append(f"### User Query:\n{user_query}")
        
        # Combine all parts
        full_prompt = "\n\n".join(prompt_parts)
        
        # Call Gemini directly with async function
        response = await call_gemini(
            full_prompt, 
            max_tokens=max_tokens, 
            temperature=temperature
        )
        
        return response
    
    async def handle_rag(self, user_query: str, max_tokens: int = 512, temperature: float = 0.7, invalid_schema_msg: str = None):
        """
        Asynchronously retrieves background + instruction docs, then calls Gemini once.
        Now handles cases where SQL validation failed.
        """
        try:
            response = await self.generate_rag_response(
                user_query, 
                max_tokens=max_tokens, 
                temperature=temperature, 
                invalid_schema_msg=invalid_schema_msg
            )
            
            # Return a Python dictionary, not a JSON string
            yield {
                "type": "text",
                "content": response
            }
        except Exception as e:
            print(f"Error in RAG service: {str(e)}")
            yield {
                "type": "text",
                "content": f"I'm sorry, I encountered an error while processing your request. Please try again later."
            }
