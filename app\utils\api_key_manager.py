import time
# List of API keys
# https://aistudio.google.com/app/apikey


class APIKeyManager:
    def __init__(self, api_keys, requests_per_minute=60):
        self.api_keys = api_keys
        self.requests_per_minute = requests_per_minute
        self.usage_tracking = {key: {
            'request_timestamps': [],
            'exhausted': False,
            'reset_time': None,
            'error_count': 0,
            'quota_exceeded': False
        } for key in api_keys}
        self.current_key_index = 0
        self.last_key_switch_time = time.time()
        self.min_delay = 0.1  # Minimum delay between requests
        self.current_api_key = None  # Track the current API key

    def mark_key_success(self, key):
        """Reset error tracking after successful API call"""
        if key in self.usage_tracking:
            self.usage_tracking[key].update({
                'error_count': 0,
                'exhausted': False,
                'reset_time': None
            })

    def get_next_available_key(self):
        current_time = time.time()
        start_index = self.current_key_index
        keys_tried = 0

        while keys_tried < len(self.api_keys):
            key = self.api_keys[self.current_key_index]
            data = self.usage_tracking[key]

            # Clean old timestamps more efficiently
            current_window = current_time - 60
            data['request_timestamps'] = [t for t in data['request_timestamps'] if t > current_window]

            if not data['quota_exceeded'] and len(data['request_timestamps']) < self.requests_per_minute:
                data['request_timestamps'].append(current_time)
                self.last_key_switch_time = current_time
                self.current_api_key = key  # Store the current API key
                return key

            self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
            keys_tried += 1
            time.sleep(self.min_delay)  # Reduced sleep time

        if keys_tried >= len(self.api_keys):
            for data in self.usage_tracking.values():
                if current_time - (data.get('reset_time') or 0) > 60:
                    data['error_count'] = 0
                    data['exhausted'] = False

            time.sleep(0.5)  # Reduced wait time
            return self.get_next_available_key()

        return None

    def mark_key_error(self, key, error_message):
        if key not in self.usage_tracking:
            return

        data = self.usage_tracking[key]
        error_message = error_message.lower()

        # Check for quota exhaustion
        if "quota" in error_message or "limit exceeded" in error_message:
            data['quota_exceeded'] = True
            print(f"\nKey {key[:10]}... has exceeded quota and is disabled.")
        else:
            # For rate limiting, mark as temporarily exhausted
            data['error_count'] += 1
            if data['error_count'] >= 3:  # Reset after 3 errors
                data['exhausted'] = True
                data['reset_time'] = time.time() + 60  # 60 second cooldown
                print(f"\nKey {key[:10]}... temporarily disabled for 60s due to rate limiting.")

        # Move to next key immediately
        self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)

    def get_key_status(self):
        current_time = time.time()
        available = 0
        rate_limited = 0
        quota_exceeded = 0

        for key, data in self.usage_tracking.items():
            if data['quota_exceeded']:
                quota_exceeded += 1
            elif data['exhausted'] and data['reset_time'] and data['reset_time'] > current_time:
                rate_limited += 1
            else:
                available += 1

        print(f"\nAPI Key Status:")
        print(f"Total Keys: {len(self.api_keys)}")
        print(f"Available: {available}")
        print(f"Rate Limited: {rate_limited}")
        print(f"Quota Exceeded: {quota_exceeded}")

        if rate_limited > 0:
            print("\nRate limited keys:")
            for key, data in self.usage_tracking.items():
                if data['exhausted'] and data['reset_time']:
                    remaining = max(0, data['reset_time'] - current_time)
                    print(f"Key {key[:10]}...: {remaining:.1f}s remaining")

