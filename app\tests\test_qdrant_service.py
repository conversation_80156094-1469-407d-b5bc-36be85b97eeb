import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.abspath("."))

# Import the QdrantService class and config
from services.qdrant_service import QdrantService
from utils.config import DEFAULT_SCHEMA_COLUMNS, DEFAULT_ROUTER_EXAMPLES

# async def test_schema_search():
#     """Test the schema search functionality"""
#     qdrant_service = QdrantService()
    
#     # Test queries
#     test_queries = [
#         "What was the energy production in June?",
#         "Show me the load shedding data",
#         "Compare power generation across sites"
#     ]
    
#     print(f"Using default limit of {DEFAULT_SCHEMA_COLUMNS} columns from config")
    
#     for query in test_queries:
#         print(f"\nTesting query: '{query}'")
        
#         # Get relevant columns using default from config
#         results = await qdrant_service.get_relevant_schema_columns(query)
        
#         print(f"Found {len(results)} relevant columns")
        
#         # Print results
#         for i, result in enumerate(results):
#             print(f"Result {i+1}:")
#             print(f"  Formatted: {result['formatted']}")
#             print(f"  Table: {result['table']}")
#             print(f"  Column: {result['column']}")
#             print(f"  Data Type: {result['dtype']}")
#             print(f"  Score: {result['score']:.4f}")

# async def test_router_examples():
#     """Test the router examples retrieval functionality"""
#     qdrant_service = QdrantService()
    
#     # Test queries
#     test_queries = [
#         "What was the energy production in June?",
#         "How many sites have good communication?",
#         "Tell me about load shedding"
#     ]
    
#     print(f"\n\n=== Testing Router Examples Retrieval ===")
#     print(f"Using default limit of {DEFAULT_ROUTER_EXAMPLES} examples from config")
    
#     for query in test_queries:
#         print(f"\nTesting query: '{query}'")
        
#         # Get similar router examples using default from config
#         results = await qdrant_service.retrieve_similar_router_examples(query)
        
#         print(f"Found {len(results)} similar router examples")
        
#         # Print results
#         for i, result in enumerate(results[:3]):  # Show only first 3 for brevity
#             print(f"Result {i+1}:")
#             print(f"  User Query: {result['user_query']}")
#             print(f"  Response: {result['response_full_text']}")
#             print(f"  Score: {result.get('score', 0)}")
#             print(f"  Requires DB: {result.get('requires_db', 'NO')}")
#             print(f"  Similarity Score: {result['similarity_score']:.4f}")
        
#         if len(results) > 3:
#             print(f"  ... and {len(results) - 3} more results")

async def test_fetch_router_instructions():
    """Test fetching router instructions from the router_instructions collection"""
    qdrant_service = QdrantService()
    
    print("\n\n=== Testing Router Instructions Fetch ===")
    
    # Fetch router instructions
    instructions = await qdrant_service.fetch_router_instructions()
    
    # Check if instructions were retrieved
    if instructions:
        print(f"Successfully retrieved router instructions")
        print(f"Instructions length: {len(instructions)} characters")
        
        # Print the first 100 characters as a preview
        preview = instructions[:100] + "..." if len(instructions) > 100 else instructions
        print(f"Preview: {preview}")
    else:
        print("No router instructions found in the database")

if __name__ == "__main__":
    # Run all tests
    # asyncio.run(test_schema_search())
    # asyncio.run(test_router_examples())
    asyncio.run(test_fetch_router_instructions())


