import pandas as pd
import sys
import os
import asyncio
import re
from pathlib import Path
from docx import Document  # Correct import for python-docx

# Add the parent directory to sys.path to import from app
sys.path.append(str(Path(__file__).parent.parent.parent))

# Import the call_gemini function from your application
from app.utils.llm_utils import call_gemini # Changed remember to undo
from app.utils.utils import clean_output_response # Changed remember to undo
from app.services.qdrant_service import QdrantService

def read_docx(file_path):
    """Extract text from a Word document"""
    try:
        doc = Document(file_path)
        full_text = []
        for para in doc.paragraphs:
            full_text.append(para.text)
        return '\n'.join(full_text)
    except Exception as e:
        print(f"Error reading Word document: {str(e)}")
        return "Error reading document"

async def test_csv_standalone():
    """Test reading a CSV file and preparing it for analysis without using application services"""
    print("=== Testing CSV File Analysis with Knowledge Base ===")
    
    # Path to the CSV file and Word document
    csv_path = r"C:\Users\<USER>\Desktop\Fesco\Chatbot\app\fesco_data - Sheet1.csv"
    # docx_path = r"C:\Users\<USER>\Desktop\Fesco\Chatbot\app\FESCO SubDivision Losses Data Analysis.docx"
    
    # Check if files exist
    if not os.path.exists(csv_path):
        print(f"CSV file not found at: {csv_path}")
        print("Please check the path and try again.")
        return
        
    # if not os.path.exists(docx_path):
    #     print(f"Word document not found at: {docx_path}")
    #     print("Please check the path and try again.")
    #     return
    
    # Read the CSV file into a DataFrame
    print(f"\nReading CSV file from: {csv_path}")
    df = pd.read_csv(csv_path)
    print(df)
    df = df.dropna(how='all')


    # First, replace all '-' strings with NaN
    print("\n=== REPLACING '-' VALUES ===")
    for col in df.columns:
        # Check if the column is of object (string) type
        if df[col].dtype == 'object':
            # Count values before replacement
            dash_count_before = df[col].astype(str).str.strip().isin(['-']).sum()
            if dash_count_before > 0:
                print(f"Column '{col}': Found {dash_count_before} values that are '-'")
                
                # Replace values that are exactly '-' after stripping whitespace
                df[col] = df[col].astype(str).apply(
                    lambda x: None if x.strip() == '-' else x
                )
                
                # Count values after replacement
                dash_count_after = df[col].astype(str).str.strip().isin(['-']).sum()
                print(f"  After replacement: {dash_count_after} values remain as '-'")

    # 2. Now handle NaN values
    print("\n=== HANDLING NaN VALUES ===")
    # For numeric columns
    numeric_columns = df.select_dtypes(include=['float64', 'int64']).columns
    if not numeric_columns.empty:
        print(f"Filling NaN values in numeric columns with 0: {list(numeric_columns)}")
        df[numeric_columns] = df[numeric_columns].fillna(0)

    # For non-numeric columns
    non_numeric_columns = df.select_dtypes(exclude=['float64', 'int64']).columns
    if not non_numeric_columns.empty:
        print(f"Filling NaN values in non-numeric columns with '0': {list(non_numeric_columns)}")
        df[non_numeric_columns] = df[non_numeric_columns].fillna('0')

    # 3. Remove commas from numeric strings and convert to float
    print("\n=== CONVERTING NUMERIC STRINGS ===")
    for col in df.columns:
        if df[col].dtype == 'object':
            # Check if the column might contain numeric values with commas
            sample = df[col].dropna().astype(str).head(5)
            if any(',' in str(x) for x in sample):
                print(f"Column '{col}' may contain numeric values with commas")
                try:
                    # Try to convert string numbers with commas to float
                    df[col] = df[col].astype(str).str.replace(',', '').replace('', None).astype(float)
                    print(f"  Successfully converted '{col}' to numeric")
                except:
                    print(f"  Could not convert '{col}' to numeric - keeping as is")
    





    # # Then replace NaN values with 0 for numeric columns
    # numeric_columns = df.select_dtypes(include=['float64', 'int64']).columns
    # df[numeric_columns] = df[numeric_columns].fillna(0)
    
    # # For non-numeric columns that had '-', replace NaN with '0' or appropriate placeholder
    # non_numeric_columns = df.select_dtypes(exclude=['float64', 'int64']).columns
    # df[non_numeric_columns] = df[non_numeric_columns].fillna('0')
    
    # # Remove commas from numeric strings and convert to float
    # for col in df.columns:
    #     if df[col].dtype == 'object':
    #         # Try to convert string numbers with commas to float
    #         try:
    #             df[col] = df[col].str.replace(',', '').astype(float)
    #         except:
    #             # If conversion fails, keep as is
    #             pass
    
    # print(f"Data cleaned: Replaced '-' values with 0 and converted numeric strings")
    # # print(df)

    print("\n=== FULL DATAFRAME CONTENTS ===")
    pd.set_option('display.max_rows', None)  # Show all rows
    pd.set_option('display.max_columns', None)  # Show all columns
    pd.set_option('display.width', 1000)  # Set width to avoid wrapping
    pd.set_option('display.max_colwidth', None)  # Show full content of each cell
    print(df)
    print("\n=== END OF DATAFRAME CONTENTS ===")




    # # Handle NaN values appropriately
    # # 1. Drop rows where all values are NaN
    # df = df.dropna(how='all')

    # # 2. Check if the column exists before trying to fill it
    # # Option 1: Use the actual column names from the DataFrame
    # if 'Unnamed: 1' in df.columns:
    #     df['Unnamed: 1'] = df['Unnamed: 1'].fillna(method='ffill')

    # # Option 2: Rename columns to more meaningful names first
    # # Inspect the first row to see if it contains the actual column names
    # if df.iloc[0, 0] == 'Feeder Name':
    #     # The first row contains column names, use it as header
    #     new_header = df.iloc[0]
    #     df = df[1:]  # Take the data less the header row
    #     df.columns = new_header  # Set the header row as the df column names
    #     print("Renamed columns using first row as header")
        
    #     # Now we can use the proper column names
    #     df['Subdivision Name'] = df['Subdivision Name'].fillna(method='ffill')
    #     if 'DIVISION Name' in df.columns:
    #         df['DIVISION Name'] = df['DIVISION Name'].fillna(method='ffill')
    #     if 'CIRCLE Name' in df.columns:
    #         df['CIRCLE Name'] = df['CIRCLE Name'].fillna(method='ffill')
    # else:
    #     # If first row is not headers, just use the existing column names
    #     print("Using existing column names")
    #     # You might want to rename columns manually here
    #     # df = df.rename(columns={'Unnamed: 0': 'Feeder Name', 'Unnamed: 1': 'Subdivision Name', ...})

    # # 3. Only drop rows where critical data is missing
    # critical_column = 'Feeder Name' if 'Feeder Name' in df.columns else 'Unnamed: 0'
    # df = df.dropna(subset=[critical_column])

    # # 4. Replace remaining NaNs with zeros for numeric columns
    # numeric_columns = df.select_dtypes(include=['float64', 'int64']).columns
    # df[numeric_columns] = df[numeric_columns].fillna(0)

    # # Print the cleaned DataFrame info
    # print("\nCleaned DataFrame:")
    # print(f"Shape: {df.shape}")
    # print(f"Columns: {df.columns.tolist()}")
    # print(df)
    
    # # Drop rows with any empty values
    # original_row_count = len(df)
    # df = df.dropna()
    # dropped_row_count = original_row_count - len(df)
    # print(f"\nDropped {dropped_row_count} rows with empty values.")
    # print(f"Original row count: {original_row_count}, New row count: {len(df)}")
    
    # # Create a sample user query for analysis
    # user_query = "Analyze this data and identify key patterns and insights"
    
    # # Prepare the data for analysis
    # num_rows = len(df)
    # data_types = df.dtypes.to_dict()
    # summary_stats = df.describe(include='all').to_dict()
    # columns = df.columns.tolist()
    
    # # Convert the entire DataFrame to a list of dictionaries
    # all_data = df.to_dict(orient='records')
    
    # # Initialize the Qdrant service
    # qdrant_service = QdrantService()

    # # Get knowledge base from Qdrant
    # knowledge_base = await qdrant_service.get_text_by_type(
    #     "knowledge_base",
    #     "", 
    #     limit=1
    # )
    # print(f"Knowledge base retrieved: {len(knowledge_base)} characters")
    # print(f"Knowledge excerpt: {knowledge_base[:100]}...")

    # # Get instructions from Qdrant
    # instructions = await qdrant_service.get_text_by_type(
    #     "fesco_instructions", 
    #     "", 
    #     limit=1
    # )
    # print(f"Instructions retrieved: {len(instructions)} characters")
    # print(f"Instructions excerpt: {instructions[:100]}...")
    
    # # Format the instructions by replacing the knowledge_base placeholder
    # formatted_instructions = instructions.format(knowledge_base=knowledge_base)
    # print(f"Formatted instructions: {len(formatted_instructions)} characters")
    
    # # Build the complete prompt with formatted instructions and DataFrame details
    # prompt = f"""
    # {formatted_instructions}
    
    # Original user question: {user_query}
    
    # DataFrame details:
    # - Columns: {columns}
    # - Number of rows: {num_rows}
    # - Data types: {data_types}
    # - Summary statistics: {summary_stats}
    
    # Complete DataFrame data:
    # {all_data}
    # """
    
    # # Calculate approximate token count (rough estimate: 4 chars ≈ 1 token)
    # approx_tokens = len(prompt) / 4
    # print(f"\nApproximate token count: {approx_tokens:.0f}")
    
    # # Check if we might exceed token limits
    # if approx_tokens > 30000:  # Conservative limit for Gemini
    #     print("\nWARNING: Prompt may exceed token limits. Consider using a subset of the data.")
    #     # Fallback to using a sample if the data is too large
    #     sample_size = min(100, len(df))
    #     sample_data = df.sample(sample_size).to_dict(orient='records')
    #     prompt = f"""
    #     {formatted_instructions}
        
    #     Original user question: {user_query}
        
    #     DataFrame details:
    #     - Columns: {columns}
    #     - Number of rows: {num_rows}
    #     - Data types: {data_types}
    #     - Summary statistics: {summary_stats}
        
    #     Sample of DataFrame data ({sample_size} rows):
    #     {sample_data}
    #     """
    #     print(f"Using a sample of {sample_size} rows instead of the full dataset.")
    
    # # Print what would be sent to the model
    # print("\n=== Data and Prompt Being Sent to Model ===")
    # print(f"\nUser Query: {user_query}")
    # print(f"\nPrompt excerpt (first 500 chars):")
    # print(prompt[:500] + "...")
    
    # # Actually call the model using call_gemini
    # print("\n=== Calling Gemini API ===")
    # try:
    #     response = await call_gemini(prompt, max_tokens=1024, temperature=0.3)
        
    #     # Clean the response
    #     cleaned_response = clean_output_response(response)
        
    #     # Additional cleaning for asterisks if needed
    #     cleaned_response = cleaned_response.replace('*', '•')
        
    #     print("\n=== Analysis Result (Cleaned) ===")
    #     print(cleaned_response)
    # except Exception as e:
    #     print(f"\nError calling Gemini API: {str(e)}")

if __name__ == "__main__":
    # Run the test
    asyncio.run(test_csv_standalone())










