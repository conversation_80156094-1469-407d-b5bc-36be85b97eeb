import asyncio
import pandas as pd
from app.utils.llm_utils import call_gemini
from app.utils.qdrant_connection import client
from sentence_transformers import SentenceTransformer

class AnalysisService:
    """Service for analyzing dataframe results and providing textual insights"""
    
    def __init__(self):
        self.encoder = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")
        self.instructions_collection = "analysis_instructions"
    
    async def retrieve_analysis_instructions(self):
        """
        Retrieves the standard analysis instructions from Qdrant
        """
        try:
            # Try a simple scroll request without filters
            scroll_results = await asyncio.to_thread(
                client.scroll,
                collection_name=self.instructions_collection,
                limit=10,
                with_payload=True
            )
            
            # Check if any points exist and have the right type
            if scroll_results[0]:
                for point in scroll_results[0]:
                    if point.payload and point.payload.get("type") == "analysis_instructions":
                        return point.payload.get("text", "")
            
            # Return default instructions if not found
            print("No analysis instructions found in Qdrant, using default")
            return """
            You are a data analyst examining query results. Provide a concise, insightful analysis of this data.
            
            Provide a clear, concise analysis that highlights:
            1. Key patterns or trends
            2. Notable outliers or anomalies
            3. Direct answers to the user's question based on the data
            4. Any limitations in the data that affect the analysis
            
            Keep your analysis under 200 words and focus on the most relevant insights.
            """
                
        except Exception as e:
            print(f"Error retrieving analysis instructions: {str(e)}")
            # Return default instructions if retrieval fails
            return """
            You are a data analyst examining query results. Provide a concise, insightful analysis of this data.
            
            Provide a clear, concise analysis that highlights:
            1. Key patterns or trends
            2. Notable outliers or anomalies
            3. Direct answers to the user's question based on the data
            4. Any limitations in the data that affect the analysis
            
            Keep your analysis under 200 words and focus on the most relevant insights.
            """
    
    async def analyze_data(self, df: pd.DataFrame, user_query: str, sql_query: str = None):
        """
        Analyzes dataframe and returns textual insights using LLM
        
        Args:
            df (pd.DataFrame): The dataframe to analyze
            user_query (str): The original user question
            sql_query (str, optional): The SQL query that generated the data
            
        Returns:
            str: Textual analysis of the data
        """
        # Prepare dataframe metadata for the prompt
        num_rows = len(df)
        data_types = df.dtypes.to_dict()
        summary_stats = df.describe(include='all').to_dict()
        sample_data = df.head(5).to_dict(orient='records')
        columns = df.columns.tolist()
        
        # Retrieve analysis instructions from Qdrant
        instructions = await self.retrieve_analysis_instructions()
        
        # Build prompt for the LLM
        prompt = f"""
        {instructions}
        
        Original user question: {user_query}
        
        DataFrame details:
        - Columns: {columns}
        - Number of rows: {num_rows}
        - Data types: {data_types}
        - Summary statistics: {summary_stats}
        - Sample rows: {sample_data}
        """
        
        # Call Gemini to analyze the data
        analysis = await call_gemini(prompt, max_tokens=512, temperature=0.3)
        
        # Return the analysis
        return analysis
    
    async def handle_analysis(self, df: pd.DataFrame, user_query: str, sql_query: str = None):
        """
        Handles the analysis request and yields results for the frontend
        
        Args:
            df (pd.DataFrame): The dataframe to analyze
            user_query (str): The original user question
            sql_query (str, optional): The SQL query that generated the data
            
        Yields:
            dict: Analysis result in the expected format
        """
        try:
            analysis = await self.analyze_data(df, user_query, sql_query)
            
            # Yield the analysis in the expected format
            yield {
                "type": "analysis",
                "content": analysis
            }
        except Exception as e:
            print(f"Error in analysis service: {str(e)}")
            yield {
                "type": "analysis",
                "content": "I couldn't analyze this data due to an error. Please try again with a different query."
            }





