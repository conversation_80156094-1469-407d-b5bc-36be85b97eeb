import json
import os
import time
import uuid
import pandas as pd
import psycopg2
import traceback
import re
import asyncio
import asyncpg
from pathlib import Path
from services.plotly_service import PlotlyService
from services.rag_service import RagService
from utils.config import DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASS, CRUD_KEYWORDS, OUTPUT_DIR, MAX_RETRIES, DEFAULT_SIMILAR_QUERIES
from utils.llm_utils import call_gemini
from utils.utils import generate_csv_filename, clean_sentence
# from utils.utils import clean_output_response
from services.qdrant_service import QdrantService
from sqlglot import transpile
from sqlglot.errors import ParseError
from services.analysis_service import AnalysisService

class SQLService:
    """Service for SQL query generation, validation, and execution"""
    
    def __init__(self):
        self.qdrant_service = QdrantService()
        self.rag_service = RagService()
        self.plotly_service = PlotlyService()
        self.analysis_service = AnalysisService()
        self._pool = None  # Connection pool for asyncpg
    
    async def get_pool(self):
        """Get or create the database connection pool"""
        if self._pool is None:
            self._pool = await asyncpg.create_pool(
                host=DB_HOST,
                port=DB_PORT,
                database=DB_NAME,
                user=DB_USER,
                password=DB_PASS,
                min_size=5,
                max_size=20
            )
        return self._pool
    
    def clean_sql_code(self, sql_text: str) -> str:
        """Extracts SQL code from a text that might contain markdown or explanations"""
        # If the response contains SQL code blocks, extract them
        if "```sql" in sql_text.lower():
            # Extract content between ```sql and ```
            sql_blocks = re.findall(r"```sql\s*(.*?)\s*```", sql_text, re.DOTALL)
            if sql_blocks:
                return sql_blocks[0].strip()
        
        # If no SQL code blocks, look for SELECT statements
        select_match = re.search(r"(SELECT\s+.*?)(;|$)", sql_text, re.DOTALL | re.IGNORECASE)
        if select_match:
            return select_match.group(0).strip()
            
        # If all else fails, return the original text
        return sql_text.strip()
    
    def is_crud_operation(self, query: str) -> bool:
        """Check if query contains CRUD keywords"""
        query_lower = query.lower()
        return any(keyword in query_lower for keyword in CRUD_KEYWORDS)
    
    def _is_same_query(self, query1: str, query2: str) -> bool:
        """Simple check if two queries are essentially the same"""
        # Normalize queries by removing whitespace and converting to lowercase
        q1 = re.sub(r'\s+', ' ', query1.lower()).strip()
        q2 = re.sub(r'\s+', ' ', query2.lower()).strip()
        
        # Check if they're identical or very similar
        return q1 == q2 or (len(q1) > 0 and len(q2) > 0 and (q1 in q2 or q2 in q1))

    async def generate_sql_query(self, query: str, error_context: str = None, retry_count: int = 0) -> str:
        """
        Generates an SQL query from a user query, using schema and schema linker info from Qdrant.
        Supports retries with error context.
        
        Args:
            query (str): The user's natural language query
            error_context (str): Error message from previous attempt, if any
            retry_count (int): Current retry attempt number
        
        Returns:
            str: Generated SQL query
        """
        
        filtered_query = clean_sentence(query)
        
        # Start building the full prompt
        full_prompt_parts = []
        
        # NEW: Get instructions using the new function instead of get_text_by_type
        instructions_list = await self.qdrant_service.get_query_instructions("instructions")
        instructions = "\n\n".join([item["text"] for item in instructions_list]) if instructions_list else ""
        
        # Add instructions first if available
        if instructions:
            full_prompt_parts.append(f"### SQL Generation Instructions:\n{instructions}")
        
        # Step 1: Retrieve System Prompt and add it after instructions
        system_prompt = await self.qdrant_service.retrieve_system_prompt(
            collection_name="knowledge_documents"
        )
        full_prompt_parts.append(system_prompt)

        # Step 2: Build dynamic prompt with clear sections
        prompt_sections = []
        
        # First add schema relationships (REORDERED)
        schema_linker = await self.qdrant_service.get_text_by_type(
            "schema_linker", 
            filtered_query, 
            limit=3
        )
        
        if schema_linker:
            prompt_sections.append(f"### Schema Relationships and Context:\n{schema_linker}")
        
        # Get schema and schema linker information using the actual query for better relevance
        schema_columns = await self.qdrant_service.get_relevant_schema_columns(
            filtered_query
        )
        
        # Format schema columns for the prompt (AFTER schema_linker)
        if schema_columns:
            schema_info = "### Relevant Database Columns:\n"
            for col in schema_columns:
                schema_info += f"- {col['formatted']}\n"
            prompt_sections.append(schema_info)
        
        # Get similar pre-made queries to help guide the model
        similar_queries = await self.qdrant_service.retrieve_similar_queries(
            filtered_query
        )
        
        
        # Add similar queries as examples if available
        if similar_queries:
            examples_section = "### Similar Query Examples:\n"
            for i, example in enumerate(similar_queries):
                examples_section += f"\nExample {i+1}:\n"
                examples_section += f"Question: {example['natural_language']}\n"
                examples_section += f"SQL: {example['sql_query']}\n"
            prompt_sections.append(examples_section)
        
        # Combine all sections
        prompt_body = "\n\n".join(prompt_sections)
        full_prompt_parts.append(prompt_body)

        # Step 3: Get SQL guidelines from query_instructions instead of hardcoded standard_instructions
        sql_guidelines_list = await self.qdrant_service.get_query_instructions("sql_guidelines")
        sql_guidelines = "\n\n".join([item["text"] for item in sql_guidelines_list]) if sql_guidelines_list else ""
        
        # Use fetched SQL guidelines or fall back to default if none found
        if sql_guidelines:
            full_prompt_parts.append(f"### SQL Guidelines:\n{sql_guidelines}")
        
        # Add error context for retries
        if error_context and retry_count > 0:
            error_section = (
                f"### Error Information:\n"
                f"Previous SQL query attempt failed with error: {error_context}\n"
                f"This is retry attempt {retry_count} of {MAX_RETRIES}.\n"
                f"Please fix the SQL query to address this error.\n"
            )
            full_prompt_parts.append(error_section)

        # Add user query at the end for focus
        user_query_section = (
            f"### User Query:\n{query}\n\n"
            f"Generate a SQL query that answers this question."
        )
        full_prompt_parts.append(user_query_section)
        
        # Combine all parts with double newlines between sections
        full_prompt = "\n\n".join(full_prompt_parts)
        
        # Adjust temperature based on retry count to increase variation in responses
        temperature = 0.7 + (retry_count * 0.1)  # Increase temperature with each retry
        temperature = min(temperature, 1.0)  # Cap at 1.0
        
        # Print the full prompt
        print("\n++++++++++++++++++++++++++ Full Prompt ++++++++++++++++++++++++++++++++")
        print(full_prompt)
        print("++++++++++++++++++++++++++ End Prompt ++++++++++++++++++++++++++++++++\n")
        
        # Step 4: Call Gemini directly with async function
        sql_query = await call_gemini(full_prompt, max_tokens=500, temperature=temperature)
        
        # Print the generated SQL query
        print("\n++++++++++++++++++++++++++ Generated SQL Query ++++++++++++++++++++++++++++++++")
        print(sql_query.strip())
        print("++++++++++++++++++++++++++ End SQL Query ++++++++++++++++++++++++++++++++\n")
        
        return sql_query.strip()

    async def retry_sql_generation(self, user_query: str, error_msg: str, previous_sql: str = None, retry_count: int = 0):
        """
        Retries SQL query generation with error context.
        
        Args:
            user_query (str): Original user query
            error_msg (str): Error message from previous attempt
            previous_sql (str): The SQL query that failed
            retry_count (int): Current retry count
        
        Returns:
            tuple: (success, sql_query or error message)
        """
        if retry_count >= MAX_RETRIES:
            print(f"Max retries ({MAX_RETRIES}) exceeded for query: {user_query}")
            return False, f"Failed after {MAX_RETRIES} attempts. Last error: {error_msg}"
        
        # Create error context with previous SQL and error message
        error_context = f"Previous SQL query: {previous_sql}\nError: {error_msg}"
        
        # Add variation hints based on retry count to avoid similar queries
        if retry_count > 0:
            if "table" in error_msg.lower() and "does not exist" in error_msg.lower():
                error_context += "\nPlease check the table names carefully and only use tables mentioned in the schema."
            elif "column" in error_msg.lower() and "does not exist" in error_msg.lower():
                error_context += "\nPlease check the column names carefully and only use columns mentioned in the schema."
            elif "syntax error" in error_msg.lower():
                error_context += "\nThere's a syntax error in the SQL. Please check the query structure."
        
        try:
            # Generate new SQL with error context
            new_sql_query = await self.generate_sql_query(user_query, error_context, retry_count + 1)
            new_sql_query = self.clean_sql_code(new_sql_query)
            
            # Simple check to avoid identical queries
            if previous_sql and self._is_same_query(previous_sql, new_sql_query):
                print(f"Generated identical query on retry {retry_count + 1}. Forcing variation.")
                # Force more variation by increasing temperature significantly
                new_sql_query = await self.generate_sql_query(
                    user_query, 
                    error_context + "\nIMPORTANT: Generate a completely different query structure.", 
                    retry_count + 1
                )
                new_sql_query = self.clean_sql_code(new_sql_query)
            
            return True, new_sql_query
        except Exception as e:
            print(f"Retry {retry_count + 1} failed: {str(e)}")
            return False, str(e)

    async def validate_sql_schema(self, sql_query: str) -> tuple:
        """
        Validates that the SQL query has correct syntax.
        
        Args:
            sql_query (str): The SQL query to validate
            
        Returns:
            tuple: (is_valid, error_message)
        """
        print("\n=== Starting SQL Validation Phase ===")
        start_time = time.time()
        
        try:
            # Check if input is a string
            if not isinstance(sql_query, str):
                print("Validation failed: Input is not a string")
                return False, "Input must be a string"
            
            # Validate SQL syntax using sqlglot
            print("Validating SQL syntax using sqlglot for PostgreSQL dialect")
            # For PostgreSQL dialect specifically
            transpile(sql_query)
            
            end_time = time.time()
            validation_time = end_time - start_time
            print(f"=== SQL Validation Successful - Completed in {validation_time:.2f} seconds ===")
            return True, "SQL syntax is valid"
        except ParseError as e:
            # Collect all error messages
            error_msgs = []
            for err in e.errors:
                desc = err.get("description")
                line = err.get("line", "?")
                col = err.get("col", "?")
                error_msgs.append(f"Line {line}, Col {col}: {desc}")
            
            error_message = "; ".join(error_msgs)
            end_time = time.time()
            validation_time = end_time - start_time
            print(f"=== SQL Validation Failed - Completed in {validation_time:.2f} seconds ===")
            print(f"Error: {error_message}")
            return False, error_message
        except Exception as e:
            error_message = f"SQL syntax validation error: {str(e)}"
            end_time = time.time()
            validation_time = end_time - start_time
            print(f"=== SQL Validation Failed - Completed in {validation_time:.2f} seconds ===")
            print(f"Error: {error_message}")
            return False, error_message

    async def execute_sql_query(self, sql_query: str, working_directory: str):
        """
        Executes a SQL query and saves the results to a CSV file.
        
        Args:
            sql_query (str): The SQL query to execute
            working_directory (str): Directory where output files should be saved
        
        Returns:
            tuple: (DataFrame with query results, filename of saved CSV)
        """
        print("\n=== Starting SQL Execution Phase ===")
        start_time = time.time()
        print(f"Executing query of type: {type(sql_query)}")
        print(sql_query)
        
        # Ensure working directory exists
        working_dir = Path(working_directory)
        working_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate filename first
        file_name = generate_csv_filename()
        csv_path = working_dir / f"{file_name}.csv"
        print(f"Results will be saved to: {csv_path}")
        
        try:
            print("Getting database connection pool...")
            pool_start_time = time.time()
            pool = await self.get_pool()
            pool_time = time.time() - pool_start_time
            print(f"Connection pool acquired in {pool_time:.2f} seconds")
            
            print("Executing SQL query against database...")
            query_start_time = time.time()
            async with pool.acquire() as conn:
                # Execute query
                records = await conn.fetch(sql_query)
                query_time = time.time() - query_start_time
                print(f"Query execution completed in {query_time:.2f} seconds")
                
                if not records:
                    print("Query returned no records")
                    end_time = time.time()
                    total_time = end_time - start_time
                    print(f"=== SQL Execution Completed (Empty Result) - Total time: {total_time:.2f} seconds ===")
                    return pd.DataFrame(), file_name
                
                # Convert to DataFrame
                print(f"Converting {len(records)} records to DataFrame...")
                df_start_time = time.time()
                columns = [desc for desc in records[0].keys()]
                rows = [[record[col] for col in columns] for record in records]
                df = pd.DataFrame(rows, columns=columns)
                df_time = time.time() - df_start_time
                print(f"DataFrame conversion completed in {df_time:.2f} seconds")
                
                # Save to CSV
                print("Saving results to CSV...")
                csv_start_time = time.time()
                df.to_csv(csv_path, index=False)
                csv_time = time.time() - csv_start_time
                print(f"CSV save completed in {csv_time:.2f} seconds")
                
                end_time = time.time()
                total_time = end_time - start_time
                print(f"=== SQL Execution Completed Successfully - Total time: {total_time:.2f} seconds ===")
                print(f"DataFrame shape: {df.shape}")
                return df, file_name
                
        except Exception as e:
            end_time = time.time()
            total_time = end_time - start_time
            print(f"=== SQL Execution Failed - Total time: {total_time:.2f} seconds ===")
            print(f"Error executing SQL query: {str(e)}")
            print(traceback.format_exc())
            raise

    async def forward_pass(self, user_query: str):
        """
        Processes a user query through the entire pipeline: SQL generation, execution, and visualization.
        Returns results incrementally as an async generator.
        """
        # Check for CRUD operations first
        if self.is_crud_operation(user_query):
            print("\n=== CRUD Operation Detected ===")
            print(f"Query: {user_query}")
            
            # Direct RAG call with specific message for CRUD
            rag_response = await self.rag_service.generate_rag_response(
                user_query, 
                invalid_schema_msg="This appears to be a data modification request. For security reasons, I can only provide read-only access to the database. Let me help you find relevant information instead."
            )
            
            yield {
                "type": "text",
                "content": rag_response
            }
            return

        # Add a single initial response
        yield {
            "type": "text",
            "content": f"Analyzing your question about: {user_query}"
        }
        
        # STEP 1: Generate SQL query (with retries if needed)
        sql_query = None
        generation_error = None
        retry_count = 0
        
        print("\n=== Starting SQL Generation Phase ===")
        
        while retry_count <= MAX_RETRIES:
            try:
                # Initial generation attempt
                if retry_count == 0:
                    print("\n=== Generating SQL Query ===")
                    sql_query = await self.generate_sql_query(user_query)
                else:
                    # Retry with error context
                    print(f"\n=== Retrying SQL Generation (Attempt {retry_count}) ===")
                    
                    success, result = await self.retry_sql_generation(
                        user_query, 
                        generation_error, 
                        sql_query, 
                        retry_count - 1
                    )
                    if success:
                        sql_query = result
                    else:
                        generation_error = result
                        retry_count += 1
                        continue
                
                # Clean and validate the SQL query
                sql_query = self.clean_sql_code(sql_query)
                
                # Basic validation
                if not sql_query or len(sql_query.strip()) < 10:
                    print("\n=== SQL Generation Failed: Empty or too short ===")
                    if retry_count < MAX_RETRIES:
                        generation_error = "Generated SQL query is empty or too short"
                        retry_count += 1
                        continue
                    else:
                        # Fall back to RAG after max retries
                        print("\n=== Falling back to RAG after SQL generation failure ===")
                        rag_response = await self.rag_service.generate_rag_response(
                            user_query, 
                            invalid_schema_msg=f"Could not generate a valid SQL query after {MAX_RETRIES} attempts."
                        )
                        
                        yield {
                            "type": "text",
                            "content": rag_response
                        }
                        return
                
                # If we got here, we have a valid SQL query
                break
                
            except Exception as e:
                print(f"\n=== SQL Generation Error (Attempt {retry_count + 1}) ===")
                print(f"Error: {str(e)}")
                print(traceback.format_exc())
                
                if retry_count < MAX_RETRIES:
                    generation_error = str(e)
                    retry_count += 1
                    continue
                else:
                    # Direct RAG call here
                    print("\n=== Falling back to RAG after SQL generation exception ===")
                    rag_response = await self.rag_service.generate_rag_response(
                        user_query, 
                        invalid_schema_msg=f"Could not generate SQL after {MAX_RETRIES} attempts: {str(e)}"
                    )
                    
                    yield {
                        "type": "text",
                        "content": rag_response
                    }
                    return
        
        # STEP 2: Validate and execute SQL query
        execution_retry_count = 0
        df = None
        file_name = None
        
        print("\n=== Starting SQL Execution Phase ===")
        
        while execution_retry_count <= MAX_RETRIES:
            try:
                # Validate SQL query against schema
                print("\n=== Validating SQL Query ===")
                # FIXED: Changed validate_sql_query to validate_sql_schema
                is_valid, error_msg = await self.validate_sql_schema(sql_query)
                
                if not is_valid:
                    print(f"\n=== Schema Validation Error (Attempt {execution_retry_count + 1}) ===")
                    print(f"Error: {error_msg}")
                    
                    if execution_retry_count < MAX_RETRIES:
                        # Try to regenerate SQL with validation error context
                        
                        print("Attempting to regenerate SQL with validation error context...")
                        success, result = await self.retry_sql_generation(
                            user_query, 
                            error_msg, 
                            sql_query, 
                            execution_retry_count
                        )
                        
                        if success:
                            sql_query = result
                            execution_retry_count += 1
                            continue  # Try again with new SQL
                        else:
                            validation_error = result
                    
                    # Only use RAG to handle the error gracefully
                    print("\n=== Falling back to RAG after schema validation failure ===")
                    rag_response = await self.rag_service.generate_rag_response(
                        user_query, 
                        invalid_schema_msg=error_msg
                    )

                    yield {
                        "type": "text",
                        "content": rag_response
                    }
                    return

                # SQL Execution phase
                print("Setting up working directory...")
                working_directory = Path(OUTPUT_DIR)
                working_directory.mkdir(parents=True, exist_ok=True)
                working_directory_str = str(working_directory)
                
                # Execute SQL query
                try:
                    print("Executing SQL query...")
                    df, file_name = await self.execute_sql_query(sql_query, working_directory_str)
                    print(f"Query executed successfully. Results shape: {df.shape if df is not None else 'None'}")
                except Exception as exec_error:
                    print(f"\n=== SQL Execution Error (Attempt {execution_retry_count + 1}) ===")
                    print(f"Error: {str(exec_error)}")
                    print(traceback.format_exc())
                    
                    if execution_retry_count < MAX_RETRIES:
                        # Try to regenerate SQL with execution error context
                        
                        print("Attempting to regenerate SQL with execution error context...")
                        success, result = await self.retry_sql_generation(
                            user_query, 
                            str(exec_error), 
                            sql_query, 
                            execution_retry_count
                        )
                        
                        if success:
                            sql_query = result
                            execution_retry_count += 1
                            continue  # Try again with new SQL
                        else:
                            # If regeneration failed, increment and try again or fall back
                            execution_retry_count += 1
                            if execution_retry_count <= MAX_RETRIES:
                                continue

                    # If we've exhausted retries, fall back to RAG
                    # Don't expose the raw error message to the user
                    print("\n=== Falling back to RAG after SQL execution error ===")
                    rag_response = await self.rag_service.generate_rag_response(
                        user_query, 
                        invalid_schema_msg=f"Database error: {str(exec_error)}"
                    )

                    yield {
                        "type": "text",
                        "content": rag_response
                    }
                    return
                
                # If we got here, there was no exception, so check if df is empty
                if df is None or df.empty:
                    print("\n=== Empty Results ===")
                    
                    if execution_retry_count < MAX_RETRIES:
                        # Try to regenerate SQL for empty results
                        
                        print("Attempting to regenerate SQL for empty results...")
                        success, result = await self.retry_sql_generation(
                            user_query, 
                            "Query executed successfully but returned no results", 
                            sql_query, 
                            execution_retry_count
                        )
                        
                        if success:
                            sql_query = result
                            execution_retry_count += 1
                            continue  # Try again with new SQL
                        else:
                            # If regeneration failed, increment and try again or fall back
                            execution_retry_count += 1
                            if execution_retry_count <= MAX_RETRIES:
                                continue
                    
                    # Direct RAG call for empty results after retries
                    print("\n=== Falling back to RAG after empty results ===")
                    rag_response = await self.rag_service.generate_rag_response(
                        user_query, 
                        invalid_schema_msg="The query executed successfully but returned no results after multiple attempts."
                    )
                    
                    yield {
                        "type": "text",
                        "content": rag_response
                    }
                    return

                print("\n=== Query Executed Successfully ===")
                print(f"Results shape: {df.shape}")
                
                break  # Success, exit the retry loop
                
            except Exception as db_error:
                print(f"\n=== Database Connection Error (Attempt {execution_retry_count + 1}) ===")
                print(f"Error: {str(db_error)}")
                print(traceback.format_exc())
                
                if execution_retry_count < MAX_RETRIES:
                    # Try to regenerate SQL with database error context
                    
                    print("Attempting to regenerate SQL with database error context...")
                    success, result = await self.retry_sql_generation(
                        user_query, 
                        str(db_error), 
                        sql_query, 
                        execution_retry_count
                    )
                    
                    if success:
                        sql_query = result
                        execution_retry_count += 1
                        continue  # Try again with new SQL
                    else:
                        # If regeneration failed, increment and try again or fall back
                        execution_retry_count += 1
                        if execution_retry_count <= MAX_RETRIES:
                            continue
                
                # Handle database connection errors with RAG after retries
                print("\n=== Falling back to RAG after database connection error ===")
                rag_response = await self.rag_service.generate_rag_response(
                    user_query, 
                    invalid_schema_msg=f"Could not connect to the database after multiple attempts: {str(db_error)}"
                )

                yield {
                    "type": "text",
                    "content": rag_response
                }
                return
        
        # STEP 3: Show SQL results to the user
        print("\n=== Preparing to Send Results to User ===")
        if df is not None and not df.empty:
            print("Converting dataframe to JSON...")
            df_json = df.to_json(orient='records')
            print("Sending dataframe results to user...")
            yield {
                "type": "dataframe",
                "content": df_json
            }
            
            # Add SQL query for reference
            yield {
                "type": "sql",
                "content": sql_query
            }
            
            # STEP 4: Generate visualization if appropriate
            print("\n=== Generating Visualization ===")
            try:
                success, viz_result = await self.plotly_service.generate_visualization_with_retry(
                    sql_query, user_query, file_name, str(working_directory)
                )
                
                if success:
                    print("Visualization generated successfully")
                    # Read the JSON file and send its contents
                    json_path = Path(working_directory) / f"{file_name}.json"
                    if json_path.exists():
                        try:
                            with open(json_path, 'r') as f:
                                json_content = f.read()
                            yield {
                                "type": "json",
                                "content": json_content
                            }
                            print("Visualization JSON sent to frontend")
                        except Exception as json_error:
                            print(f"Error reading visualization JSON: {str(json_error)}")
                            # Call RAG fallback for visualization JSON error
                            viz_error_msg = f"Could not read visualization JSON: {str(json_error)}"
                            print(f"\n=== Falling back to RAG after visualization JSON error ===")
                            rag_response = await self.rag_service.generate_rag_response(
                                user_query,
                                invalid_schema_msg=viz_error_msg
                            )
                            yield {
                                "type": "text",
                                "content": rag_response
                            }
                    else:
                        print("Visualization JSON file not found")
                        # Call RAG fallback for missing JSON file
                        viz_error_msg = "Visualization JSON file not found"
                        print(f"\n=== Falling back to RAG after missing JSON file ===")
                        rag_response = await self.rag_service.generate_rag_response(
                            user_query,
                            invalid_schema_msg=viz_error_msg
                        )
                        yield {
                            "type": "text",
                            "content": rag_response
                        }
                else:
                    print(f"Visualization generation failed: {viz_result}")
                    # Call RAG fallback for visualization generation failure
                    print(f"\n=== Falling back to RAG after visualization generation failure ===")
                    rag_response = await self.rag_service.generate_rag_response(
                        user_query,
                        invalid_schema_msg=f"Could not generate visualization: {viz_result}"
                    )
                    yield {
                        "type": "text",
                        "content": rag_response
                    }
            except Exception as viz_error:
                print(f"Error in visualization generation: {str(viz_error)}")
                # Call RAG fallback for visualization exception
                print(f"\n=== Falling back to RAG after visualization exception ===")
                rag_response = await self.rag_service.generate_rag_response(
                    user_query,
                    invalid_schema_msg=f"Error in visualization generation: {str(viz_error)}"
                )
                yield {
                    "type": "text",
                    "content": rag_response
                }
            
            # STEP 5: Generate data analysis
            print("\n=== Generating Data Analysis ===")
            try:
                async for analysis_result in self.analysis_service.handle_analysis(df, user_query, sql_query):
                    yield analysis_result
            except Exception as analysis_error:
                print(f"Error generating analysis: {str(analysis_error)}")
                # Use RAG fallback for consistency with other error handling
                print(f"\n=== Falling back to RAG after analysis error ===")
                rag_response = await self.rag_service.generate_rag_response(
                    user_query,
                    invalid_schema_msg=f"Could not generate analysis: {str(analysis_error)}"
                )
                yield {
                    "type": "text",
                    "content": rag_response
                }