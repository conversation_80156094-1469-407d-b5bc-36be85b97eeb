/* Centered title and subtitle */
.centered-title {
    text-align: center;
    color: #5c6984;
    font-weight: 600;
    margin-bottom: 10px;
    width: 100%;
}

.centered-subtitle {
    text-align: center;
    color: #8a94a6;
    font-weight: 400;
    margin-bottom: 20px;
    width: 100%;
}

/* Main navigation buttons */
button[key="main_nav_dashboard"],
button[key="main_nav_thunderbot"] {
    background: transparent;
    border-radius: 6px;
    border: 1px solid #383838;
    color: white;
    font-weight: bold;
    transition: all 0.3s ease;
    margin-bottom: 10px;
    padding-top: 5px;
    padding-bottom: 5px;
}

button[key="main_nav_dashboard"]:hover,
button[key="main_nav_thunderbot"]:hover {
    background: transparent;
    color: #5c6984;
    border-color: #BDD5FC;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}



.nav-link {
    display: inline-block;
    padding: 0.25rem 1rem;
    background: transparent;
    color: white !important;
    text-decoration: none !important;
    border-radius: 6px;
    width: 100%;
    text-align: center;
    transition: all 0.3s ease;
    border-color: #383838;
    border-style: solid;
    border-width: 1px;
    padding-top: 5px;
    padding-bottom: 5px;
}

.nav-link:hover {
    background: transparent;
    color: #7792e3 !important;
    border-style: solid;
    border-width: 1px;
    border-color: #7792e3 !important;
}

/* New Analysis button styling */
button[key="new_analysis"] {
    background: transparent !important;
    border-radius: 6px !important;
    border: 1px solid #383838 !important;
    color: white !important;
    font-weight: bold !important;
    transition: all 0.3s ease !important;
    padding-top: 5px !important;
    padding-bottom: 5px !important;
}

button[key="new_analysis"]:hover {
    background: transparent !important;
    color: #5c6984 !important;
    border-color: #BDD5FC !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}


/* Remove top padding from sidebar */
section[data-testid="stSidebar"] .css-1d391kg {
    padding-top: 0;
  }
  
  /* Reduce top margin for the first sidebar element */
  section[data-testid="stSidebar"] > div:first-child {
    margin-top: -30;
    padding-top: 0;
  }
  
  /* Ensure the logo container has no top margin */
  section[data-testid="stSidebar"] img:first-of-type {
    margin-top: -50px;
  }

  button[kind="headerNoPadding"] {
    position: absolute;
    margin-left: -15px;
    margin-top: -10px;
    /* top: 0.5rem; */
    /* left: 0.5rem; */
    /* right: 0.5rem; */
    z-index: 999;
  }
  