<!DOCTYPE html>
<html>
<head>
    <title>Chat Response</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .result-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .query-section {
            background-color: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Response</h1>
        
        <div class="query-section">
            <h4>Your Question:</h4>
            <p id="originalQuery"></p>
        </div>

        <div class="result-section">
            <h3>Answer</h3>
            <div id="responseText"></div>
        </div>

        <div class="text-center mt-4">
            <a href="/" class="btn btn-primary">Ask Another Question</a>
        </div>
    </div>

    <script>
        // Load the original query and results from session storage
        window.onload = function() {
            const query = sessionStorage.getItem('userQuery');
            const results = JSON.parse(sessionStorage.getItem('queryResults'));
            
            document.getElementById('originalQuery').textContent = query;
            
            // Clear previous content
            const responseTextElement = document.getElementById('responseText');
            responseTextElement.innerHTML = '';
            
            // Process each result
            results.forEach(result => {
                if (typeof result === 'object' && result !== null) {
                    // If it's an object with content property
                    if (result.content) {
                        const paragraph = document.createElement('p');
                        paragraph.textContent = result.content;
                        responseTextElement.appendChild(paragraph);
                    } else {
                        // If it's an object without content property
                        const paragraph = document.createElement('p');
                        paragraph.textContent = JSON.stringify(result, null, 2);
                        responseTextElement.appendChild(paragraph);
                    }
                } else {
                    // If it's a string or other primitive
                    const paragraph = document.createElement('p');
                    paragraph.textContent = result;
                    responseTextElement.appendChild(paragraph);
                }
            });
        }
    </script>
</body>
</html>
