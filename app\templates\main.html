<!DOCTYPE html>
<html>
<head>
    <title>Query Assistant</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .main-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .query-form {
            background-color: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="logo">
            <h1>⚡ Query Assistant</h1>
            <p class="text-muted">Ask me anything - I'll determine if you need data analysis or general information</p>
        </div>
        
        <div class="query-form">
            <form id="mainForm">
                <div class="mb-3">
                    <label for="query" class="form-label">What would you like to know?</label>
                    <textarea 
                        class="form-control" 
                        id="query" 
                        rows="4" 
                        placeholder="Enter your question here..."
                        required></textarea>
                </div>
                <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-lg">Ask Question</button>
                </div>
            </form>
        </div>

        <div id="loadingIndicator" class="text-center mt-4 d-none">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Analyzing your question...</p>
        </div>
    </div>

    <script>
        document.getElementById('mainForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const query = document.getElementById('query').value;
            const loadingIndicator = document.getElementById('loadingIndicator');
            
            try {
                // Show loading indicator
                loadingIndicator.classList.remove('d-none');
                
                const response = await fetch('/api/process', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ prompt: query })
                });

                const data = await response.json();
                
                if (data.error) {
                    alert(data.error);
                    return;
                }

                // Store the query and results in session storage
                sessionStorage.setItem('userQuery', query);
                sessionStorage.setItem('queryResults', JSON.stringify(data.results));

                // Redirect based on response type
                if (data.type === 'db_query') {
                    window.location.href = '/sql_view';
                } else {
                    window.location.href = '/chat_view';
                }

            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while processing your request.');
            } finally {
                loadingIndicator.classList.add('d-none');
            }
        });
    </script>
</body>
</html>
