import asyncio
import pytest
import aiohttp
import time
import uuid
import json
from datetime import datetime

# Configuration
API_URL = "http://localhost:5012/api/process"  # Update with your actual API endpoint
NUM_USERS = 5  # Number of concurrent users
REQUESTS_PER_USER = 3  # Number of requests each user will make

# Different query types to test
QUERY_TYPES = {
    "database": [
        "Show me the average solar kWh for each project over the past 7 days",
        "What is the fuel consumption for each site last month?",
        "Compare the NAR values across all projects"
    ],
    "knowledge": [
        "What factors affect solar production?",
        "How does weather impact energy generation?",
        "Explain load shedding and its effects"
    ],
    "conversational": [
        "Hello, how are you today?",
        "What can you help me with?",
        "Tell me about yourself"
    ]
}

async def simulate_user(user_id, session):
    """Simulate a single user making multiple requests"""
    results = []
    
    for i in range(REQUESTS_PER_USER):
        # Select a query type and a specific query
        query_type = list(QUERY_TYPES.keys())[i % len(QUERY_TYPES)]
        query = QUERY_TYPES[query_type][i % len(QUERY_TYPES[query_type])]
        
        payload = {
            "prompt": query,
            "user_id": user_id
        }
        
        start_time = time.time()
        
        try:
            async with session.post(API_URL, json=payload) as response:
                status = response.status
                if status == 200:
                    data = await response.json()
                    response_type = data.get("type", "unknown")
                    results_count = len(data.get("results", []))
                else:
                    response_type = "error"
                    results_count = 0
                
                elapsed = time.time() - start_time
                
                results.append({
                    "user_id": user_id,
                    "request_num": i + 1,
                    "query_type": query_type,
                    "status": status,
                    "response_type": response_type,
                    "results_count": results_count,
                    "elapsed_time": elapsed
                })
                
                print(f"User {user_id} - Request {i+1}: {status} {response_type} in {elapsed:.2f}s")
                
        except Exception as e:
            print(f"User {user_id} - Request {i+1} failed: {str(e)}")
            results.append({
                "user_id": user_id,
                "request_num": i + 1,
                "query_type": query_type,
                "status": "exception",
                "response_type": "error",
                "results_count": 0,
                "elapsed_time": time.time() - start_time,
                "error": str(e)
            })
    
    return results

@pytest.mark.asyncio
async def test_concurrent_requests():
    """Test that the API can handle multiple concurrent requests"""
    print(f"Starting concurrency test with {NUM_USERS} users, {REQUESTS_PER_USER} requests each")
    print(f"Total requests: {NUM_USERS * REQUESTS_PER_USER}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)
    
    start_time = time.time()
    
    async with aiohttp.ClientSession() as session:
        tasks = []
        for i in range(NUM_USERS):
            user_id = f"test_user_{uuid.uuid4().hex[:8]}"
            tasks.append(simulate_user(user_id, session))
        
        all_results = await asyncio.gather(*tasks)
    
    # Flatten results
    results = [item for sublist in all_results for item in sublist]
    
    # Calculate statistics
    total_time = time.time() - start_time
    success_count = sum(1 for r in results if r["status"] == 200)
    error_count = len(results) - success_count
    avg_time = sum(r["elapsed_time"] for r in results) / len(results)
    
    print("\n" + "=" * 50)
    print(f"Concurrency Test Results:")
    print(f"Total time: {total_time:.2f} seconds")
    print(f"Total requests: {len(results)}")
    print(f"Successful requests: {success_count} ({success_count/len(results)*100:.1f}%)")
    print(f"Failed requests: {error_count} ({error_count/len(results)*100:.1f}%)")
    print(f"Average request time: {avg_time:.2f} seconds")
    print("=" * 50)
    
    # Save results to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"concurrency_test_results_{timestamp}.json"
    with open(filename, "w") as f:
        json.dump({
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "num_users": NUM_USERS,
                "requests_per_user": REQUESTS_PER_USER,
                "total_time": total_time,
                "avg_time": avg_time,
                "success_rate": success_count / len(results)
            },
            "results": results
        }, f, indent=2)
    
    print(f"Detailed results saved to {filename}")
    
    # Assertions to verify the test passed
    assert success_count > 0, "No successful requests"
    assert success_count / len(results) >= 0.8, "Success rate below 80%"

if __name__ == "__main__":
    # Run the test directly without pytest
    asyncio.run(test_concurrent_requests())
