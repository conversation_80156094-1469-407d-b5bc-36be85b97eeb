import base64
from pathlib import Path
import pandas as pd
import os
from helpers.pg import database
import json

def img_to_bytes(img_path):
    """Convert an image to a base64-encoded string."""
    img_bytes = Path(img_path).read_bytes()
    encoded = base64.b64encode(img_bytes).decode()
    return encoded

def img_to_html(img_path):
    """Generate HTML for displaying the base64-encoded image."""
    img_html = "<img src='data:image/png;base64,{}' class='img-fluid'>".format(
      img_to_bytes(img_path)
    )
    return img_html


def loadsessionsfromdb(userid):
    try:
      userid = int(userid)
    except:
      return "error"
    query = f"""
          SELECT 
            NULL "date",
          NULL as sessions,
              NULL as sessionid
        FROM thunderbotlogs
        WHERE userid = {userid} 
        ORDER BY createdat DESC
    """
    pg = database(os.getcwd())
    df = pg.read_sql_query_to_df(query=query)
    # print(df)
    return df

def delete_session_from_db(session_id):
    try:
        query = f"""
            DELETE FROM thunderbotlogs
            WHERE sessionid = '{session_id}'
        """
        pg = database(os.getcwd())
        pg.run_query(query)
        return True
    except Exception as e:
        print(f"Error deleting session from database: {e}")
        return False

def load_chat_to_db(userid, sessionid, prompt, response, sql, html):
   # Properly escape single quotes for SQL
   prompt = prompt.replace("'", "''")
   response = response.replace("'", "''")
   sql = sql.replace("'", "''") if sql else ""
   html = html.replace("'", "''") if html else ""
   
   query = f"""UPDATE thunderbotlogs
    SET session = 
        CASE 
            WHEN session IS NULL THEN 
                jsonb_build_object('1', 
                    jsonb_build_object(
                        'prompt', '{prompt}',
                        'response', '{response}',
                        'sql', '{sql}',
                        'html', '{html}'
                    )
                )
            ELSE 
                session || jsonb_build_object(
                    (COALESCE(max_key, 0) + 1)::text,
                    jsonb_build_object(
                        'prompt', '{prompt}',
                        'response', '{response}',
                        'sql', '{sql}',
                        'html', '{html}'
                    )
                )
        END
    FROM (
        SELECT MAX(key::integer) AS max_key
        FROM thunderbotlogs, jsonb_each(session)
        WHERE userid = {userid} AND sessionid = '{sessionid}'
    ) AS subquery
    WHERE userid = {userid} AND sessionid = '{sessionid}';"""
   pg = database(os.getcwd())
   pg.run_query(query=query)

def load_new_session_to_db(df,userid,table_name="thunderbotlogs"):
    df = df[['sessions','sessionid']].rename(columns={"sessions":"alias"})
    df['userid'] = userid
    pg = database(os.getcwd())
    pg.load_data_to_db(table_name=table_name,df=df,col_types=pg.get_table_column_types(table_name),temptable=f"{table_name}temp")


def append_if_absent(df, column_name, search_string,new_row,userid):
    """
    Checks if a string is absent in the specified column of the DataFrame.
    If absent, appends a new row with the string to the DataFrame.
    
    :param df: pandas DataFrame
    :param column_name: The column to search in
    :param search_string: The string to search for
    :return: The updated DataFrame
    """
    # Check if the string is found in the column
    found = df[column_name].str.contains(search_string, case=False, na=False).any()
    
    # If the string is absent, append a new row with the string
    if not found:
        new_data = pd.DataFrame(new_row)
        # df = df.append(new_row, ignore_index=True)
        df = pd.concat([new_data,df], ignore_index=True)
        # pg = database(os.getcwd())
        # print(df)
        load_new_session_to_db(new_data,userid)
    
    return df,not found

def loadspecificsessionfromdb(userid,sessionid):
    try:
      userid = int(userid)
    except:
      return "error"
    query = f"""
          SELECT 
            alias,
            userid,
            sessionid,
            (session_data.key)::int AS chatid,
            session_data.value->>'sql' AS sql,
            session_data.value->>'prompt' AS prompt,
            session_data.value->>'response' AS response,
            session_data.value->>'html' AS html
        FROM 
            thunderbotlogs,
            jsonb_each(session::jsonb) AS session_data(key, value)
        WHERE sessionid = '{sessionid}' and userid = {userid} 
        ORDER BY 4 ASC
    """
    pg = database(os.getcwd())
    df = pg.read_sql_query_to_df(query=query)

    df = df.sort_values(by='chatid')
    # Convert each row to the desired JSON format
    formatted_records = []
    for _, row in df.iterrows():
        # print(row['chatid'])
        formatted_records.append({
            "role": "user",
            "type": "text",
            "content": row['prompt']
        })
        formatted_records.append({
            "role": "assistant",
            "type": "text",
            "content": row['response']
        })
        if row['sql']:
          formatted_records.append({
              "role": "assistant",
              "type": "sql",
              "content": row['sql']
          })
        if row['html']:
          formatted_records.append({
              "role": "assistant",
              "type": "html",
              "content": row['html']
          })

    # Convert the list of dictionaries to JSON
    # json_data = json.dumps(formatted_records, indent=2)

    # Print or use the JSON data as needed
    # print(json_data)
    return formatted_records





# loadsessionsfromdb(290,'123e4567-e89b-12d3-a456-426655440000')
# loadsessionsfromdb(290)
# [
# {"role": "user", "type": "text", "content": f"{prompt}"},
# {"role": "assistant", "type": "text", "content": f"{response}"},
# {"role": "assistant", "type": "sql", "content": f"{sql}"},
# {"role": "assistant", "type": "html", "content": f"{html}"}
# ]
import streamlit as st
import plotly.graph_objects as go
import json
import base64
import numpy as np
from plotly.io import from_json

def decode_binary_data(binary_data, dtype='f8'):
    """Decode binary data from base64 format"""
    try:
        decoded_data = base64.b64decode(binary_data)
        return np.frombuffer(decoded_data, dtype=np.dtype(dtype)).tolist()
    except Exception as e:
        print(f"Error decoding binary data: {str(e)}")
        return []

def process_data_values(data_obj):
    """Process data values, handling both regular and binary formats"""
    if isinstance(data_obj, dict) and 'bdata' in data_obj:
        # Binary data format
        dtype = data_obj.get('dtype', 'f8')
        return decode_binary_data(data_obj['bdata'], dtype)
    else:
        # Regular data format
        return data_obj

def create_plotly_trace(trace_data):
    """Create a Plotly trace from JSON trace data"""
    trace_type = trace_data.get('type', 'scatter')
    
    # Common attributes for most trace types
    common_attrs = {
        'name': trace_data.get('name', ''),
        'showlegend': trace_data.get('showlegend', True),
        'legendgroup': trace_data.get('legendgroup', None),
        'hovertemplate': trace_data.get('hovertemplate', None),
    }
    
    # Process marker attributes if they exist
    marker_attrs = {}
    if 'marker' in trace_data:
        marker = trace_data['marker']
        marker_attrs = {
            'marker_color': marker.get('color'),
            'marker_size': marker.get('size'),
            'marker_symbol': marker.get('symbol'),
            'marker_line_color': marker.get('line', {}).get('color'),
            'marker_line_width': marker.get('line', {}).get('width'),
        }
        # Remove None values
        marker_attrs = {k: v for k, v in marker_attrs.items() if v is not None}
    
    # Process data values
    x_values = process_data_values(trace_data.get('x', []))
    y_values = process_data_values(trace_data.get('y', []))
    z_values = process_data_values(trace_data.get('z', []))
    
    # Create the trace based on its type
    if trace_type == 'bar':
        return go.Bar(
            x=x_values,
            y=y_values,
            orientation=trace_data.get('orientation', 'v'),
            textposition=trace_data.get('textposition', 'auto'),
            **common_attrs,
            **marker_attrs
        )
    
    elif trace_type == 'scatter':
        return go.Scatter(
            x=x_values,
            y=y_values,
            mode=trace_data.get('mode', 'markers'),
            line=trace_data.get('line', {}),
            **common_attrs,
            **marker_attrs
        )
    
    elif trace_type == 'pie':
        return go.Pie(
            labels=x_values,
            values=y_values,
            textinfo=trace_data.get('textinfo', 'percent'),
            **common_attrs
        )
    
    elif trace_type == 'histogram':
        return go.Histogram(
            x=x_values,
            y=y_values,
            histnorm=trace_data.get('histnorm', ''),
            **common_attrs,
            **marker_attrs
        )
    
    elif trace_type == 'box':
        return go.Box(
            x=x_values,
            y=y_values,
            boxmean=trace_data.get('boxmean', False),
            **common_attrs,
            **marker_attrs
        )
    
    elif trace_type == 'heatmap':
        return go.Heatmap(
            x=x_values,
            y=y_values,
            z=z_values,
            colorscale=trace_data.get('colorscale', 'Viridis'),
            **common_attrs
        )
    
    elif trace_type == 'contour':
        return go.Contour(
            x=x_values,
            y=y_values,
            z=z_values,
            contours=trace_data.get('contours', {}),
            colorscale=trace_data.get('colorscale', 'Viridis'),
            **common_attrs
        )
    
    elif trace_type == 'surface':
        return go.Surface(
            x=x_values,
            y=y_values,
            z=z_values,
            colorscale=trace_data.get('colorscale', 'Viridis'),
            **common_attrs
        )
    
    elif trace_type == 'scatter3d':
        return go.Scatter3d(
            x=x_values,
            y=y_values,
            z=z_values,
            mode=trace_data.get('mode', 'markers'),
            **common_attrs,
            **marker_attrs
        )
    
    # Default to scatter if type not recognized
    else:
        st.warning(f"Unrecognized chart type: {trace_type}. Defaulting to scatter.")
        return go.Scatter(
            x=x_values,
            y=y_values,
            mode='markers',
            **common_attrs,
            **marker_attrs
        )

def load_plotly_figure(json_data):
    """Convert JSON data to a Plotly figure"""
    # If already parsed (dict), use it directly, otherwise parse it
    
    content_data = json_data['content']
    # Parse the nested content if it's a string
    if isinstance(content_data, str):
        content = json.loads(content_data)
    else:
        content = content_data
    # print(content)
    
    # For plain JSON with data and layout
    if 'data' in content and isinstance(content['data'], list):
        # Convert JSON to Plotly figure
        fig = from_json(json.dumps(content))
        return fig
    else:
        # Handle case where JSON structure is different
        try:
            # Try to create a figure from the entire content
            fig = from_json(json.dumps(content))
            return fig
        except Exception as e:
            # If all else fails, create a basic empty figure with error message
            fig = go.Figure()
            fig.add_annotation(
                text=f"Could not parse JSON data: {str(e)}",
                xref="paper", yref="paper",
                x=0.5, y=0.5, showarrow=False
            )
            return fig

           