import os
import json
import glob
from datetime import datetime
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

RESULTS_DIR = "concurrent_test_results"

def analyze_concurrent_tests():
    """Analyze the results of concurrent tests that were run simultsaneously"""
    print("Analyzing concurrent test results...")
    
    # Create results directory if it doesn't exist
    os.makedirs(RESULTS_DIR, exist_ok=True)
    
    # Find all completed test files
    database_files = glob.glob(os.path.join(RESULTS_DIR, "*_database_results.json"))
    knowledge_files = glob.glob(os.path.join(RESULTS_DIR, "*_knowledge_results.json"))
    
    if not database_files or not knowledge_files:
        print("Not enough test results found. Please run both database and knowledge tests.")
        return
    
    # Group files by test ID to find concurrent runs
    test_ids = set()
    for file in database_files + knowledge_files:
        test_id = os.path.basename(file).split("_")[0]
        test_ids.add(test_id)
    
    print(f"Found {len(test_ids)} test IDs")
    
    # Collect all results
    all_results = []
    
    for file in database_files + knowledge_files:
        with open(file, 'r') as f:
            data = json.load(f)
            
            # Add client type to metadata
            client_type = "database" if "_database_" in file else "knowledge"
            data["metadata"]["client_type"] = client_type
            
            # Add to results list
            all_results.append(data)
    
    # Convert to DataFrame for easier analysis
    metadata_df = pd.DataFrame([r["metadata"] for r in all_results])
    
    # Flatten all individual requests
    requests = []
    for result in all_results:
        for req in result["results"]:
            # Make sure client_type is included
            if "client_type" not in req:
                req["client_type"] = result["metadata"]["client_type"]
            # Make sure test_id is included
            if "test_id" not in req:
                req["test_id"] = result["metadata"]["test_id"]
            requests.append(req)
    
    requests_df = pd.DataFrame(requests)
    
    # Convert timestamp strings to datetime objects
    requests_df["timestamp"] = pd.to_datetime(requests_df["timestamp"])
    requests_df = requests_df.sort_values("timestamp")
    
    # Calculate time windows where both clients were active
    print("\nAnalyzing time windows where both clients were active:")
    
    # Create a directory for plots
    plots_dir = os.path.join(RESULTS_DIR, "plots")
    os.makedirs(plots_dir, exist_ok=True)
    
    # Summary statistics for all tests
    summary_data = []
    
    for test_id in test_ids:
        test_requests = requests_df[requests_df["test_id"] == test_id]
        
        if len(test_requests) == 0:
            continue
            
        db_requests = test_requests[test_requests["client_type"] == "database"]
        knowledge_requests = test_requests[test_requests["client_type"] == "knowledge"]
        
        if len(db_requests) == 0 or len(knowledge_requests) == 0:
            print(f"Test ID {test_id}: Only one client type found, skipping")
            continue
        
        # Find overlapping time window
        db_start = db_requests["timestamp"].min()
        db_end = db_requests["timestamp"].max()
        knowledge_start = knowledge_requests["timestamp"].min()
        knowledge_end = knowledge_requests["timestamp"].max()
        
        overlap_start = max(db_start, knowledge_start)
        overlap_end = min(db_end, knowledge_end)
        
        overlap_duration = (overlap_end - overlap_start).total_seconds()
        
        if overlap_duration <= 0:
            print(f"Test ID {test_id}: No overlap between database and knowledge tests")
            continue
        
        # Count requests in the overlap window
        db_overlap = db_requests[(db_requests["timestamp"] >= overlap_start) & 
                                (db_requests["timestamp"] <= overlap_end)]
        knowledge_overlap = knowledge_requests[(knowledge_requests["timestamp"] >= overlap_start) & 
                                             (knowledge_requests["timestamp"] <= overlap_end)]
        
        db_overlap_count = len(db_overlap)
        knowledge_overlap_count = len(knowledge_overlap)
        
        print(f"\nTest ID: {test_id}")
        print(f"Overlap window: {overlap_start} to {overlap_end} ({overlap_duration:.2f} seconds)")
        print(f"Database requests during overlap: {db_overlap_count}")
        print(f"Knowledge requests during overlap: {knowledge_overlap_count}")
        print(f"Total concurrent requests: {db_overlap_count + knowledge_overlap_count}")
        
        # Calculate average response times during overlap
        db_overlap_avg = db_overlap["elapsed_time"].mean()
        knowledge_overlap_avg = knowledge_overlap["elapsed_time"].mean()
        
        # Calculate average response times outside overlap
        db_non_overlap = db_requests[(db_requests["timestamp"] < overlap_start) | 
                                    (db_requests["timestamp"] > overlap_end)]
        knowledge_non_overlap = knowledge_requests[(knowledge_requests["timestamp"] < overlap_start) | 
                                                 (knowledge_requests["timestamp"] > overlap_end)]
        
        db_non_overlap_avg = db_non_overlap["elapsed_time"].mean() if len(db_non_overlap) > 0 else 0
        knowledge_non_overlap_avg = knowledge_non_overlap["elapsed_time"].mean() if len(knowledge_non_overlap) > 0 else 0
        
        print(f"Database avg response time during overlap: {db_overlap_avg:.3f}s")
        print(f"Knowledge avg response time during overlap: {knowledge_overlap_avg:.3f}s")
        
        if len(db_non_overlap) > 0:
            print(f"Database avg response time outside overlap: {db_non_overlap_avg:.3f}s")
            print(f"Database slowdown during concurrency: {(db_overlap_avg/db_non_overlap_avg - 1)*100:.1f}%")
        
        if len(knowledge_non_overlap) > 0:
            print(f"Knowledge avg response time outside overlap: {knowledge_non_overlap_avg:.3f}s")
            print(f"Knowledge slowdown during concurrency: {(knowledge_overlap_avg/knowledge_non_overlap_avg - 1)*100:.1f}%")
        
        # Add to summary data
        summary_data.append({
            "test_id": test_id,
            "overlap_duration": overlap_duration,
            "db_requests": db_overlap_count,
            "knowledge_requests": knowledge_overlap_count,
            "total_requests": db_overlap_count + knowledge_overlap_count,
            "requests_per_second": (db_overlap_count + knowledge_overlap_count) / overlap_duration,
            "db_avg_time": db_overlap_avg,
            "knowledge_avg_time": knowledge_overlap_avg,
            "db_non_overlap_avg": db_non_overlap_avg,
            "knowledge_non_overlap_avg": knowledge_non_overlap_avg,
            "db_slowdown": (db_overlap_avg/db_non_overlap_avg - 1)*100 if db_non_overlap_avg > 0 else 0,
            "knowledge_slowdown": (knowledge_overlap_avg/knowledge_non_overlap_avg - 1)*100 if knowledge_non_overlap_avg > 0 else 0
        })
        
        # Create visualizations
        
        # 1. Timeline of requests
        plt.figure(figsize=(12, 6))
        
        # Plot database requests
        plt.scatter(db_requests["timestamp"], 
                   np.ones(len(db_requests)) * 1, 
                   color='blue', 
                   alpha=0.7, 
                   label='Database Queries')
        
        # Plot knowledge requests
        plt.scatter(knowledge_requests["timestamp"], 
                   np.ones(len(knowledge_requests)) * 2, 
                   color='green', 
                   alpha=0.7, 
                   label='Knowledge Queries')
        
        # Highlight overlap region
        plt.axvspan(overlap_start, overlap_end, color='yellow', alpha=0.3, label='Overlap Period')
        
        plt.yticks([1, 2], ['Database', 'Knowledge'])
        plt.title(f'Request Timeline - Test ID: {test_id}')
        plt.xlabel('Time')
        plt.ylabel('Query Type')
        plt.legend()
        plt.tight_layout()
        plt.savefig(os.path.join(plots_dir, f'{test_id}_timeline.png'))
        plt.close()
        
        # 2. Response time comparison
        plt.figure(figsize=(10, 6))
        
        # Create data for box plots
        box_data = [
            db_non_overlap["elapsed_time"] if len(db_non_overlap) > 0 else [],
            db_overlap["elapsed_time"],
            knowledge_non_overlap["elapsed_time"] if len(knowledge_non_overlap) > 0 else [],
            knowledge_overlap["elapsed_time"]
        ]
        
        box_labels = [
            'DB (Non-Overlap)', 
            'DB (Overlap)', 
            'Knowledge (Non-Overlap)', 
            'Knowledge (Overlap)'
        ]
        
        plt.boxplot(box_data, labels=box_labels)
        plt.title(f'Response Time Comparison - Test ID: {test_id}')
        plt.ylabel('Response Time (seconds)')
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig(os.path.join(plots_dir, f'{test_id}_response_times.png'))
        plt.close()
        
        # 3. Request rate over time
        plt.figure(figsize=(12, 6))
        
        # Create time bins (every 5 seconds)
        min_time = min(db_start, knowledge_start)
        max_time = max(db_end, knowledge_end)
        
        # Create a combined dataframe with 5-second bins
        all_test_requests = pd.concat([db_requests, knowledge_requests])
        all_test_requests['time_bin'] = pd.cut(
            (all_test_requests['timestamp'] - min_time).dt.total_seconds(),
            bins=np.arange(0, (max_time - min_time).total_seconds() + 5, 5)
        )
        
        # Count requests per bin
        request_counts = all_test_requests.groupby(['time_bin', 'client_type']).size().unstack(fill_value=0)
        
        # Plot stacked bar chart
        request_counts.plot(kind='bar', stacked=True, figsize=(12, 6), 
                           color=['blue', 'green'])
        
        plt.title(f'Request Rate Over Time - Test ID: {test_id}')
        plt.xlabel('Time Bin (5-second intervals)')
        plt.ylabel('Number of Requests')
        plt.legend(['Database', 'Knowledge'])
        plt.tight_layout()
        plt.savefig(os.path.join(plots_dir, f'{test_id}_request_rate.png'))
        plt.close()
    
    # Create summary report if we have data
    if summary_data:
        summary_df = pd.DataFrame(summary_data)
        
        # Save summary to CSV
        summary_df.to_csv(os.path.join(RESULTS_DIR, 'concurrency_summary.csv'), index=False)
        
        print("\n=== OVERALL SUMMARY ===")
        print(f"Total tests analyzed: {len(summary_df)}")
        print(f"Average requests per second: {summary_df['requests_per_second'].mean():.2f}")
        print(f"Average database response time: {summary_df['db_avg_time'].mean():.3f}s")
        print(f"Average knowledge response time: {summary_df['knowledge_avg_time'].mean():.3f}s")
        print(f"Average database slowdown: {summary_df['db_slowdown'].mean():.1f}%")
        print(f"Average knowledge slowdown: {summary_df['knowledge_slowdown'].mean():.1f}%")
        
        # Create summary visualizations
        
        # 1. Slowdown comparison across tests
        plt.figure(figsize=(10, 6))
        
        x = np.arange(len(summary_df))
        width = 0.35
        
        plt.bar(x - width/2, summary_df['db_slowdown'], width, label='Database Slowdown')
        plt.bar(x + width/2, summary_df['knowledge_slowdown'], width, label='Knowledge Slowdown')
        
        plt.xlabel('Test ID')
        plt.ylabel('Slowdown (%)')
        plt.title('Performance Impact During Concurrent Operation')
        plt.xticks(x, summary_df['test_id'], rotation=45)
        plt.legend()
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig(os.path.join(plots_dir, 'overall_slowdown_comparison.png'))
        plt.close()
        
        # 2. Requests per second vs response time
        plt.figure(figsize=(10, 6))
        
        plt.scatter(summary_df['requests_per_second'], summary_df['db_avg_time'], 
                   label='Database', color='blue', s=100, alpha=0.7)
        plt.scatter(summary_df['requests_per_second'], summary_df['knowledge_avg_time'], 
                   label='Knowledge', color='green', s=100, alpha=0.7)
        
        plt.xlabel('Requests per Second')
        plt.ylabel('Average Response Time (s)')
        plt.title('Response Time vs. Request Rate')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.tight_layout()
        plt.savefig(os.path.join(plots_dir, 'response_time_vs_request_rate.png'))
        plt.close()
        
        print(f"\nAnalysis complete. Visualizations saved to {plots_dir}")
        print(f"Summary data saved to {os.path.join(RESULTS_DIR, 'concurrency_summary.csv')}")

if __name__ == "__main__":
    analyze_concurrent_tests()
