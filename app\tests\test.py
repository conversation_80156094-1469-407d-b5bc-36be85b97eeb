# import sys
# import os

# # Add the root project directory to sys.path
# project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# if project_root not in sys.path:
#     sys.path.insert(0, project_root)

# # Try both import styles
# try:
#     # Try absolute import first
#     from app.services.qdrant_service import fetch_all_points, get_text_by_type
# except ImportError:
#     try:
#         # If that fails, try adding app directory and using relative import
#         app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
#         if app_dir not in sys.path:
#             sys.path.insert(0, app_dir)
#         from services.qdrant_service import fetch_all_points, get_text_by_type
#     except ImportError:
#         # If both fail, print helpful error message
#         print("ERROR: Could not import qdrant_service. Make sure you're running from the project root.")
#         print("Try: python -m app.tests.test")
#         sys.exit(1)

# # Example usage (assuming `client` is already initialized elsewhere):
# all_schema_embeddings = fetch_all_points("schema_embeddings", batch_size=500)
# print(len(all_schema_embeddings))
