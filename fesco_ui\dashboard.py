import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import hydralit_components as hc
import os
from streamlit_elements import elements, dashboard, mui, html, lazy
import copy
import time
from datetime import datetime
import json
import base64
import streamlit.components.v1 as components
from dotenv import load_dotenv

# Load environment variables for navigation only
load_dotenv()
DASHBOARD_URL = os.environ.get("DASHBOARD_URL", "http://localhost:8501")
THUNDERBOT_URL = os.environ.get("THUNDERBOT_URL", "http://localhost:5015")

# Initialize all session state variables at the beginning of the script
if "layout_timestamp" not in st.session_state:
    print("Creating new layout_timestamp")
    st.session_state.layout_timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
else:
    print(f"Using existing layout_timestamp: {st.session_state.layout_timestamp}")

if "dashboard_layouts_saved" not in st.session_state:
    st.session_state.dashboard_layouts_saved = False

# Define default layouts as constants first
DEFAULT_KPI_LAYOUT = [
    {"i": "kpi1", "x": 0, "y": 0, "w": 3, "h": 1},
    {"i": "kpi2", "x": 3, "y": 0, "w": 3, "h": 1},
    {"i": "kpi3", "x": 6, "y": 0, "w": 3, "h": 1},
    {"i": "kpi4", "x": 9, "y": 0, "w": 3, "h": 1},
]

DEFAULT_CHART_LAYOUT = [
    {"i": "loss_trend", "x": 0, "y": 0, "w": 12, "h": 8},
    {"i": "units_comparison", "x": 0, "y": 8, "w": 6, "h": 8},
    {"i": "units_lost", "x": 6, "y": 8, "w": 6, "h": 8},
]

DEFAULT_TABLES_LAYOUT = [
    {"i": "high_loss", "x": 0, "y": 0, "w": 6, "h": 4},
    {"i": "low_loss", "x": 6, "y": 0, "w": 6, "h": 4},
    {"i": "negative_loss", "x": 0, "y": 4, "w": 12, "h": 4},
]

# Then initialize the temp variables
if "temp_kpi_layout" not in st.session_state:
    st.session_state.temp_kpi_layout = copy.deepcopy(DEFAULT_KPI_LAYOUT)
if "temp_chart_layout" not in st.session_state:
    st.session_state.temp_chart_layout = copy.deepcopy(DEFAULT_CHART_LAYOUT)
if "temp_tables_layout" not in st.session_state:
    st.session_state.temp_tables_layout = copy.deepcopy(DEFAULT_TABLES_LAYOUT)

# Define callback functions that will update these temp variables
def update_kpi_layout(layout):
    st.session_state.temp_kpi_layout = layout
    print(f"KPI layout updated: {layout}")

def update_chart_layout(layout):
    st.session_state.temp_chart_layout = layout
    print(f"Chart layout updated: {layout}")

def update_tables_layout(layout):
    st.session_state.temp_tables_layout = layout
    print(f"Tables layout updated: {layout}")

# Function to save layouts to session state
def save_layouts_to_session():
    try:
        # First, update the main session state variables from the temp variables
        st.session_state.kpi_layout = copy.deepcopy(st.session_state.temp_kpi_layout)
        st.session_state.chart_layout = copy.deepcopy(st.session_state.temp_chart_layout)
        st.session_state.tables_layout = copy.deepcopy(st.session_state.temp_tables_layout)
        
        # Print current layouts before saving
        print("Saving layouts:")
        print(f"KPI layout: {st.session_state.kpi_layout}")
        print(f"Chart layout: {st.session_state.chart_layout}")
        print(f"Tables layout: {st.session_state.tables_layout}")
        
        # Store each layout separately for easier debugging and access
        st.session_state.saved_kpi_layout = copy.deepcopy(st.session_state.kpi_layout)
        st.session_state.saved_chart_layout = copy.deepcopy(st.session_state.chart_layout)
        st.session_state.saved_tables_layout = copy.deepcopy(st.session_state.tables_layout)
        
        # Also store as a combined JSON for backward compatibility
        layouts_json = json.dumps({
            "kpi_layout": st.session_state.kpi_layout,
            "chart_layout": st.session_state.chart_layout,
            "tables_layout": st.session_state.tables_layout
        })
        
        st.session_state.saved_dashboard_layouts = layouts_json
        st.session_state.dashboard_layouts_saved = True
        
        print(f"Layouts saved successfully: {layouts_json[:100]}...")
        return True
    except Exception as e:
        print(f"Error saving layouts: {e}")
        return False

# Function to load layouts from session state
def load_layouts_from_session():
    # print("Attempting to load layouts...")
    # print(f"dashboard_layouts_saved: {st.session_state.get('dashboard_layouts_saved', False)}")
    # print(f"saved_dashboard_layouts exists: {'saved_dashboard_layouts' in st.session_state}")
    
    if "saved_dashboard_layouts" in st.session_state and st.session_state.dashboard_layouts_saved:
        try:
            serialized = st.session_state.saved_dashboard_layouts
            print(f"Serialized data: {serialized[:100]}...")
            
            layouts_dict = json.loads(serialized)
            print(f"Loaded layouts keys: {list(layouts_dict.keys())}")
            
            # Update session state with loaded layouts
            st.session_state.kpi_layout = layouts_dict["kpi_layout"]
            st.session_state.chart_layout = layouts_dict["chart_layout"]
            st.session_state.tables_layout = layouts_dict["tables_layout"]
            
            # Also update the temp variables
            st.session_state.temp_kpi_layout = copy.deepcopy(layouts_dict["kpi_layout"])
            st.session_state.temp_chart_layout = copy.deepcopy(layouts_dict["chart_layout"])
            st.session_state.temp_tables_layout = copy.deepcopy(layouts_dict["tables_layout"])
            
            print("Layouts loaded successfully")
            return True
        except Exception as e:
            print(f"Error loading layouts: {e}")
            import traceback
            traceback.print_exc()
            return False
    else:
        print("No saved layouts found")
    return False

# Initialize layouts - either from saved state or defaults
if not load_layouts_from_session():
    # Use defaults if no saved layouts
    st.session_state.kpi_layout = copy.deepcopy(DEFAULT_KPI_LAYOUT)
    st.session_state.chart_layout = copy.deepcopy(DEFAULT_CHART_LAYOUT)
    st.session_state.tables_layout = copy.deepcopy(DEFAULT_TABLES_LAYOUT)
    
    # Also initialize temp variables
    st.session_state.temp_kpi_layout = copy.deepcopy(DEFAULT_KPI_LAYOUT)
    st.session_state.temp_chart_layout = copy.deepcopy(DEFAULT_CHART_LAYOUT)
    st.session_state.temp_tables_layout = copy.deepcopy(DEFAULT_TABLES_LAYOUT)

# Load custom CSS
def load_css():
    with open("styles/dashboard_styles.css") as f:
        st.markdown(f"<style>{f.read()}</style>", unsafe_allow_html=True)

# Function to set up the dashboard sidebar using Streamlit's native sidebar
def setup_dashboard_sidebar():
    with st.sidebar:
        st.image("assets/thunder-platform.png", use_container_width=True)
        st.markdown("<div style='margin-bottom: 1rem;'></div>", unsafe_allow_html=True)

        if st.button("ThunderBot", key="main_nav_thunderbot_sidebar", use_container_width=True):
            st.markdown(f'<meta http-equiv="refresh" content="0;URL={THUNDERBOT_URL}">', unsafe_allow_html=True)
        st.markdown("<hr>", unsafe_allow_html=True)
        
        # Title for the sidebar
        st.title("Add Visualization")
        
        # Line Chart with fixed positioning
        with st.container():
            st.markdown("""
            <div class="chart-container">
                <p style="margin-bottom: 8px; font-size: 16px; color: white;">Line Chart</p>
                <div class="svg-container">
                    <svg width="100%" height="120" viewBox="0 0 200 100" xmlns="http://www.w3.org/2000/svg">
                        <path d="M10,90 L30,40 L50,60 L70,20 L90,50 L110,30 L130,70 L150,10 L170,45 L190,30" stroke="#4285F4" stroke-width="3" fill="none"/>
                        <line x1="10" y1="90" x2="190" y2="90" stroke="#ccc" stroke-width="1"/>
                        <line x1="10" y1="10" x2="10" y2="90" stroke="#ccc" stroke-width="1"/>
                    </svg>
                </div>
            </div>
            """, unsafe_allow_html=True)
            if st.button("Add Line Chart", key="sidebar_add_line_chart"):
                st.session_state.show_data_modal = True
                st.session_state.current_chart_type = "Line Chart"
                st.rerun()

        # Bar Chart with fixed positioning
        with st.container():
            st.markdown("""
            <div class="chart-container">
                <p style="margin-bottom: 8px; font-size: 16px; color: white;">Bar Chart</p>
                <div class="svg-container">
                    <svg width="100%" height="120" viewBox="0 0 200 100" xmlns="http://www.w3.org/2000/svg">
                        <rect x="20" y="20" width="20" height="70" fill="#DB4437"/>
                        <rect x="50" y="40" width="20" height="50" fill="#DB4437"/>
                        <rect x="80" y="10" width="20" height="80" fill="#DB4437"/>
                        <rect x="110" y="30" width="20" height="60" fill="#DB4437"/>
                        <rect x="140" y="50" width="20" height="40" fill="#DB4437"/>
                        <rect x="170" y="25" width="20" height="65" fill="#DB4437"/>
                        <line x1="10" y1="90" x2="190" y2="90" stroke="#ccc" stroke-width="1"/>
                        <line x1="10" y1="10" x2="10" y2="90" stroke="#ccc" stroke-width="1"/>
                    </svg>
                </div>
            </div>
            """, unsafe_allow_html=True)
            if st.button("Add Bar Chart", key="sidebar_add_bar_chart"):
                st.session_state.show_data_modal = True
                st.session_state.current_chart_type = "Bar Chart"
                st.rerun()

        # Pie Chart
        with st.container():
            st.markdown("""
            <div class="chart-container">
                <p style="margin-bottom: 8px; font-size: 16px; color: white;">Pie Chart</p>
                <div class="svg-container">
                    <svg width="100%" height="120" viewBox="0 0 200 100" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="100" cy="50" r="40" fill="transparent" stroke="#ccc" stroke-width="1"/>
                        <path d="M100,50 L100,10 A40,40 0 0,1 135.4,65 z" fill="#F4B400"/>
                        <path d="M100,50 L135.4,65 A40,40 0 0,1 100,90 z" fill="#0F9D58"/>
                        <path d="M100,50 L100,90 A40,40 0 0,1 64.6,65 z" fill="#4285F4"/>
                        <path d="M100,50 L64.6,65 A40,40 0 0,1 100,10 z" fill="#DB4437"/>
                    </svg>
                </div>
            </div>
            """, unsafe_allow_html=True)
            if st.button("Add Pie Chart", key="sidebar_add_pie_chart"):
                st.session_state.show_data_modal = True
                st.session_state.current_chart_type = "Pie Chart"
                st.rerun()

        # Heatmap
        with st.container():
            st.markdown("<p style='margin-bottom: 8px; font-size: 16px; color: white;'>Heatmap</p>", unsafe_allow_html=True)
            
            # Create a container with background styling
            with st.container():
                # Use columns to create a grid-like structure
                cols = st.columns(5)
                
                # First row
                cols[0].markdown("<div style='background-color: #FFC400; height: 20px;'></div>", unsafe_allow_html=True)
                cols[1].markdown("<div style='background-color: #FF7F02; height: 20px;'></div>", unsafe_allow_html=True)
                cols[2].markdown("<div style='background-color: #DB4437; height: 20px;'></div>", unsafe_allow_html=True)
                cols[3].markdown("<div style='background-color: #0F9D58; height: 20px;'></div>", unsafe_allow_html=True)
                cols[4].markdown("<div style='background-color: #4285F4; height: 20px;'></div>", unsafe_allow_html=True)
                
                # Second row
                cols[0].markdown("<div style='background-color: #FF7F02; height: 20px;'></div>", unsafe_allow_html=True)
                cols[1].markdown("<div style='background-color: #DB4437; height: 20px;'></div>", unsafe_allow_html=True)
                cols[2].markdown("<div style='background-color: #0F9D58; height: 20px;'></div>", unsafe_allow_html=True)
                cols[3].markdown("<div style='background-color: #4285F4; height: 20px;'></div>", unsafe_allow_html=True)
                cols[4].markdown("<div style='background-color: #FFC400; height: 20px;'></div>", unsafe_allow_html=True)
                
                # Third row
                cols[0].markdown("<div style='background-color: #DB4437; height: 20px;'></div>", unsafe_allow_html=True)
                cols[1].markdown("<div style='background-color: #0F9D58; height: 20px;'></div>", unsafe_allow_html=True)
                cols[2].markdown("<div style='background-color: #4285F4; height: 20px;'></div>", unsafe_allow_html=True)
                cols[3].markdown("<div style='background-color: #FFC400; height: 20px;'></div>", unsafe_allow_html=True)
                cols[4].markdown("<div style='background-color: #FF7F02; height: 20px;'></div>", unsafe_allow_html=True)
            
            # Add button
            if st.button("Add Heatmap", key="sidebar_add_heatmap"):
                st.session_state.show_data_modal = True
                st.session_state.current_chart_type = "Heatmap"
                st.rerun()

# Add this function to handle errors gracefully
def handle_dashboard_error(error, fallback_action=None):
    """Handles dashboard errors in a user-friendly way"""
    st.error("There was an issue loading the dashboard. Please refresh the page or contact support if the problem persists.")
    # Log the actual error for debugging (not visible to users)
    print(f"Dashboard error: {str(error)}")
    import traceback
    traceback.print_exc()
    
    # Execute fallback action if provided
    if fallback_action:
        fallback_action()

def setup_basic_dashboard():
    """Fallback function to set up a basic dashboard when the main one fails"""
    st.title("Electricity Distribution Dashboard")
    st.info("Some dashboard components couldn't be loaded. You're viewing a simplified version.")

# Only set page config if running directly (not imported)
if __name__ == "__main__":
    st.set_page_config(page_title="Electricity Distribution Monitoring", layout="wide")
    
    # Add navigation buttons at the very top of the screen
    # nav_col1, nav_col2, nav_col3 = st.columns([1, 1, 1])
    # with nav_col2:  # Center column for the button
    #     if st.button("ThunderBot", key="main_nav_thunderbot", use_container_width=True):
    #         st.markdown(f'<meta http-equiv="refresh" content="0;URL={THUNDERBOT_URL}">', unsafe_allow_html=True)
    
    # st.markdown("<hr>", unsafe_allow_html=True)
    
    # Load CSS and setup sidebar
    try:
        load_css()
        setup_dashboard_sidebar()  # This now uses Streamlit's native sidebar
    except Exception as e:
        handle_dashboard_error(e, fallback_action=setup_basic_dashboard)

# Load data
@st.cache_data
def load_data():
    df = pd.read_csv("fesco_data - Sheet1.csv")
    # Clean numeric columns by removing commas and converting to float
    for col in df.columns:
        if "Units" in col or "Loss" in col:
            if df[col].dtype == object:
                # More robust cleaning - handle spaces and dashes
                df[col] = df[col].str.strip()
                df[col] = df[col].replace({'-': '0', '': '0', '  -   ': '0'})
                # Remove commas and convert to float
                df[col] = df[col].str.replace(',', '').astype(float)
    
    # Ensure filter columns are strings and remove empty/nan values
    for col in ["CIRCLE Name", "DIVISION Name", "Subdivision Name", "Feeder Name"]:
        df[col] = df[col].fillna('')
        df[col] = df[col].astype(str)
    
    return df

df = load_data()

# Header with styled title
st.markdown("<h1 class='centered'>⚡ Electricity Distribution Monitoring</h1>", unsafe_allow_html=True)
st.markdown("<h3 class='centered-l2'>FESCO Distribution Analysis Dashboard</h3>", unsafe_allow_html=True)

# Create styled filter section
st.markdown("<h4 style='color: #5c6984; margin-top: 20px;'>Filters</h4>", unsafe_allow_html=True)

# Use a container with custom styling for filters
with st.container():
    col1, col2, col3, col4 = st.columns(4)
    
    # Filter out empty strings and 'nan' values for all options
    all_circles = [x for x in df["CIRCLE Name"].unique() if x and x.lower() != 'nan']
    all_divisions = [x for x in df["DIVISION Name"].unique() if x and x.lower() != 'nan']
    all_subdivisions = [x for x in df["Subdivision Name"].unique() if x and x.lower() != 'nan']
    all_feeders = [x for x in df["Feeder Name"].unique() if x and x.lower() != 'nan']
    
    # Initialize session state for filters if not already present
    if 'circle' not in st.session_state:
        st.session_state.circle = "All"
    if 'division' not in st.session_state:
        st.session_state.division = "All"
    if 'subdivision' not in st.session_state:
        st.session_state.subdivision = "All"
    if 'feeder' not in st.session_state:
        st.session_state.feeder = "All"
    
    # Callback functions to update dependent filters
    def on_circle_change():
        st.session_state.division = "All"
        st.session_state.subdivision = "All"
        st.session_state.feeder = "All"
    
    def on_division_change():
        st.session_state.subdivision = "All"
        st.session_state.feeder = "All"
    
    def on_subdivision_change():
        st.session_state.feeder = "All"
    
    def on_feeder_change():
        # If a specific feeder is selected, update the other filters to match
        if st.session_state.feeder != "All":
            feeder_row = df[df["Feeder Name"] == st.session_state.feeder].iloc[0]
            st.session_state.subdivision = feeder_row["Subdivision Name"]
            st.session_state.division = feeder_row["DIVISION Name"]
            st.session_state.circle = feeder_row["CIRCLE Name"]
    
    # Circle filter
    with col1:
        circle = st.selectbox(
            "Select Circle", 
            options=["All"] + sorted(all_circles),
            key="circle",
            on_change=on_circle_change
        )
    
    # Division filter - show only divisions from selected circle
    with col2:
        if circle != "All":
            division_options = [x for x in df[df["CIRCLE Name"] == circle]["DIVISION Name"].unique() 
                               if x and x.lower() != 'nan']
        else:
            division_options = all_divisions
        
        division = st.selectbox(
            "Select Division", 
            options=["All"] + sorted(division_options),
            key="division",
            on_change=on_division_change
        )
    
    # Subdivision filter - show only subdivisions from selected division
    with col3:
        filtered_df_for_subdiv = df.copy()
        if circle != "All":
            filtered_df_for_subdiv = filtered_df_for_subdiv[filtered_df_for_subdiv["CIRCLE Name"] == circle]
        if division != "All":
            filtered_df_for_subdiv = filtered_df_for_subdiv[filtered_df_for_subdiv["DIVISION Name"] == division]
        
        subdivision_options = [x for x in filtered_df_for_subdiv["Subdivision Name"].unique() 
                              if x and x.lower() != 'nan']
        
        subdivision = st.selectbox(
            "Select Subdivision", 
            options=["All"] + sorted(subdivision_options),
            key="subdivision",
            on_change=on_subdivision_change
        )
    
    # Feeder filter - show only feeders from selected subdivision
    with col4:
        filtered_df_for_feeder = df.copy()
        if circle != "All":
            filtered_df_for_feeder = filtered_df_for_feeder[filtered_df_for_feeder["CIRCLE Name"] == circle]
        if division != "All":
            filtered_df_for_feeder = filtered_df_for_feeder[filtered_df_for_feeder["DIVISION Name"] == division]
        if subdivision != "All":
            filtered_df_for_feeder = filtered_df_for_feeder[filtered_df_for_feeder["Subdivision Name"] == subdivision]
        
        feeder_options = [x for x in filtered_df_for_feeder["Feeder Name"].unique() 
                         if x and x.lower() != 'nan']
        
        feeder = st.selectbox(
            "Select Feeder", 
            options=["All"] + sorted(feeder_options),
            key="feeder",
            on_change=on_feeder_change
        )

# Filter data based on selections
filtered_df = df.copy()
if circle != "All":
    filtered_df = filtered_df[filtered_df["CIRCLE Name"] == circle]
if division != "All":
    filtered_df = filtered_df[filtered_df["DIVISION Name"] == division]
if subdivision != "All":
    filtered_df = filtered_df[filtered_df["Subdivision Name"] == subdivision]
if feeder != "All":
    filtered_df = filtered_df[filtered_df["Feeder Name"] == feeder]

# Month selector - using only one time period section
month_options = ["All", "Jan", "Feb", "Mar"]
month_index = 0  # Default to All

# Try to use hydralit components for better styling
st.markdown("<h4 style='color: #5c6984; margin-top: 20px;'>Time Period</h4>", unsafe_allow_html=True)
month = st.radio("Select Month", options=month_options, horizontal=True, index=month_index)

# Helper function to format electricity units (kWh, MWh, GWh)
def format_electricity_units(watt_hours):
    if watt_hours is None:
        return "N/A"
    
    if watt_hours < 1000:
        return f"{watt_hours:.2f} Wh"
    elif watt_hours < 1000000:
        return f"{watt_hours/1000:.2f} kWh"
    elif watt_hours < 1000000000:
        return f"{watt_hours/1000000:.2f} MWh"
    else:
        return f"{watt_hours/1000000000:.2f} GWh"

# KPI Summary Cards with styled metrics
st.markdown("<h4 style='color: #5c6984; margin-top: 20px;'>Key Performance Indicators</h4>", unsafe_allow_html=True)

# Add custom CSS for white resize handles
st.markdown("""
<style>
/* Make resize handles white */
.react-resizable-handle {
    filter: invert(1) !important;
    opacity: 0.7 !important;
}

/* Increase opacity on hover for better visibility */
.react-resizable-handle:hover {
    opacity: 1 !important;
}
</style>
""", unsafe_allow_html=True)

# Function to save layout changes
def save_kpi_layout(layout):
    st.session_state.kpi_layout = layout
    # Also update the saved layout if it exists
    if "saved_kpi_layout" in st.session_state:
        st.session_state.saved_kpi_layout = layout.copy()

# Calculate KPIs based on selected month
if month == "All":
    # Calculate totals across all months
    total_received = (filtered_df["Units Received Jan-25"].sum() + 
                     filtered_df["Units Received Feb-25"].sum() + 
                     filtered_df["Units Received Mar-25"].sum())
    
    total_sold = (filtered_df["Units Sold Jan-25"].sum() + 
                 filtered_df["Units Sold Feb-25"].sum() + 
                 filtered_df["Units Sold Mar-25"].sum())
    
    total_lost = (filtered_df["Units Lost Jan-25"].sum() + 
                 filtered_df["Units Lost Feb-25"].sum() + 
                 filtered_df["Units Lost Mar-25"].sum())
    
    avg_loss_pct = (total_lost / total_received * 100) if total_received > 0 else 0
    
    # Create KPI data
    kpi_data = {
        "kpi1": {"title": "Total Units Received (All)", "value": format_electricity_units(total_received)},
        "kpi2": {"title": "Total Units Sold (All)", "value": format_electricity_units(total_sold)},
        "kpi3": {"title": "Total Units Lost (All)", "value": format_electricity_units(total_lost)},
        "kpi4": {"title": "Avg. Distribution Loss % (All)", "value": f"{avg_loss_pct:.2f}%"},
    }
else:
    # Use the selected month
    total_received = filtered_df[f"Units Received {month}-25"].sum()
    total_sold = filtered_df[f"Units Sold {month}-25"].sum()
    total_lost = filtered_df[f"Units Lost {month}-25"].sum()
    avg_loss_pct = (total_lost / total_received * 100) if total_received > 0 else 0
    
    # Create KPI data
    kpi_data = {
        "kpi1": {"title": f"Total Units Received ({month})", "value": format_electricity_units(total_received)},
        "kpi2": {"title": f"Total Units Sold ({month})", "value": format_electricity_units(total_sold)},
        "kpi3": {"title": f"Total Units Lost ({month})", "value": format_electricity_units(total_lost)},
        "kpi4": {"title": f"Avg. Distribution Loss % ({month})", "value": f"{avg_loss_pct:.2f}%"},
    }

# Create the draggable dashboard with KPI boxes
with elements(f"kpi_dashboard_{st.session_state.layout_timestamp}"):
    # Force the layout to be used by creating a new copy
    current_kpi_layout = copy.deepcopy(st.session_state.temp_kpi_layout)
    print("Current KPI layout being used:", current_kpi_layout)
    
    with dashboard.Grid(
        layout=current_kpi_layout,
        ncols=12,
        draggableHandle=".draggable-header",
        onLayoutChange=update_kpi_layout,
    ):
        # Add KPI boxes
        for kpi_id, kpi_info in kpi_data.items():
            with mui.Paper(key=kpi_id, sx={"display": "flex", "flexDirection": "column", "height": "100%", "p": 2, 
                                          "background": "linear-gradient(to bottom, #E4EFFF, #BDD5FC)",
                                          "borderRadius": "10px",
                                          "boxShadow": "0 4px 6px rgba(0, 0, 0, 0.1)"}):
                mui.Typography(
                    kpi_info["title"],
                    className="draggable-header",
                    sx={"fontSize": 18, "fontWeight": "bold", "mb": 1, "cursor": "move", "color": "#5c6984"}
                )
                mui.Typography(
                    kpi_info["value"],
                    sx={"fontSize": 28, "fontWeight": "medium", "color": "#5c6984"}
                )

# Main visualizations with styled headers
# st.markdown("<h4 style='color: #5c6984; margin-top: 30px;'>Distribution Loss Analysis</h4>", unsafe_allow_html=True)

# Function to save chart layout changes
def save_chart_layout(layout):
    st.session_state.chart_layout = layout
    if "saved_chart_layout" in st.session_state:
        st.session_state.saved_chart_layout = layout.copy()

# Initialize toggle state if not present
if "show_all_feeders" not in st.session_state:
    st.session_state.show_all_feeders = False

# Prepare data for charts
charts_data = {}

# Chart 1: Distribution Loss Percentage by Month
if not filtered_df.empty:
    # Create a dataframe for monthly trends
    trend_data = []
    for idx, row in filtered_df.iterrows():
        feeder_name = row["Feeder Name"]
        for month_iter in ["Jan", "Feb", "Mar"]:
            loss_pct = row[f"Distribution Loss %age {month_iter}-25"]
            if pd.notna(loss_pct) and loss_pct != '-':
                trend_data.append({
                    "Feeder": feeder_name,
                    "Month": month_iter,
                    "Loss Percentage": float(loss_pct) if loss_pct != '-' else 0
                })
    
    trend_df = pd.DataFrame(trend_data)
    
    # Line chart for loss percentage trends - convert to bar chart
    if not trend_df.empty:
        # Group by month and calculate average loss percentage for each feeder
        pivot_df = trend_df.pivot_table(
            index="Month", 
            columns="Feeder", 
            values="Loss Percentage",
            aggfunc='mean'
        ).reset_index()
        
        # Convert to long format for plotting
        plot_df = pd.melt(
            pivot_df, 
            id_vars=["Month"], 
            var_name="Feeder", 
            value_name="Loss Percentage"
        )
        
        # Create bar chart with explicit color sequence
        fig1 = px.bar(
            plot_df, 
            x="Month", 
            y="Loss Percentage", 
            color="Feeder",
            title="",  # Removed internal title
            labels={"Loss Percentage": "Distribution Loss %", "Month": "Month (2025)"},
            barmode="group",  # Group bars by month
            color_discrete_sequence=px.colors.qualitative.Plotly  # Use default Plotly colors
        )
        fig1.update_layout(
            height=400,
            xaxis_title="Month",
            yaxis_title="Distribution Loss %",
            legend_title="Feeder",
            margin=dict(l=40, r=40, t=40, b=40),
        )
        charts_data["loss_trend"] = fig1

    # Chart 2: Units Received vs Sold
    if month == "All":
        # Create a dataframe with total units for all months
        all_units = filtered_df[["Feeder Name"]].copy()
        all_units["Total Units Received"] = (
            filtered_df["Units Received Jan-25"] + 
            filtered_df["Units Received Feb-25"] + 
            filtered_df["Units Received Mar-25"]
        )
        all_units["Total Units Sold"] = (
            filtered_df["Units Sold Jan-25"] + 
            filtered_df["Units Sold Feb-25"] + 
            filtered_df["Units Sold Mar-25"]
        )
        
        # Determine whether to show all feeders or just top 10
        if st.session_state.show_all_feeders:
            units_comp = all_units.sort_values("Total Units Received", ascending=False)
        else:
            units_comp = all_units.sort_values("Total Units Received", ascending=False).head(10)
        
        fig2 = px.bar(
            units_comp,
            x="Feeder Name",
            y=["Total Units Received", "Total Units Sold"],
            title="",  # Removed internal title
            barmode="group",
            labels={"value": "Units", "Feeder Name": "Feeder", "variable": "Metric"},
            color_discrete_sequence=['#636EFA', '#EF553B']
        )
        
        # Adjust layout for better feeder name display
        fig2.update_layout(
            height=400,
            xaxis_tickangle=-90,  # Changed from -45 to -90 for vertical labels
            margin=dict(l=40, r=40, t=40, b=120),  # Increased bottom margin significantly
            xaxis=dict(
                tickmode='array',
                tickvals=list(range(len(units_comp))),
                ticktext=units_comp["Feeder Name"].tolist()
            )
        )
    else:
        # For single month
        units_comp = filtered_df[["Feeder Name", f"Units Received {month}-25", f"Units Sold {month}-25"]].copy()
        
        # Determine whether to show all feeders or just top 10
        if st.session_state.show_all_feeders:
            units_comp = units_comp.sort_values(f"Units Received {month}-25", ascending=False)
        else:
            units_comp = units_comp.sort_values(f"Units Received {month}-25", ascending=False).head(10)
        
        fig2 = px.bar(
            units_comp,
            x="Feeder Name",
            y=[f"Units Received {month}-25", f"Units Sold {month}-25"],
            title="",  # Removed internal title
            barmode="group",
            labels={"value": "Units", "Feeder Name": "Feeder", "variable": "Metric"},
            color_discrete_sequence=['#636EFA', '#EF553B']
        )
        
        # Adjust layout for better feeder name display
        fig2.update_layout(
            height=400,
            xaxis_tickangle=-90,  # Changed from -45 to -90 for vertical labels
            margin=dict(l=40, r=40, t=40, b=120),  # Increased bottom margin significantly
            xaxis=dict(
                tickmode='array',
                tickvals=list(range(len(units_comp))),
                ticktext=units_comp["Feeder Name"].tolist()
            )
        )
    
    charts_data["units_comparison"] = fig2
    charts_data["units_comparison_toggle"] = True  # Flag to indicate this chart has a toggle

    # Chart 3: Area chart for Units Lost
    lost_data = []
    for idx, row in filtered_df.iterrows():
        feeder = row["Feeder Name"]
        for month_iter in ["Jan", "Feb", "Mar"]:
            lost = row[f"Units Lost {month_iter}-25"]
            if pd.notna(lost) and lost != '-':
                lost_data.append({
                    "Feeder": feeder,
                    "Month": month_iter,
                    "Units Lost": float(lost) if lost != '-' else 0
                })
    
    lost_df = pd.DataFrame(lost_data)
    
    if not lost_df.empty:
        # Group by month and sum units lost
        monthly_lost = lost_df.groupby("Month")["Units Lost"].sum().reset_index()
        
        fig3 = px.area(
            monthly_lost,
            x="Month",
            y="Units Lost",
            title="",  # Removed internal title
            labels={"Units Lost": "Units Lost", "Month": "Month (2025)"},
            color_discrete_sequence=['#AB63FA']  # Purple for area chart
        )
        fig3.update_layout(
            height=400,
            margin=dict(l=40, r=40, t=40, b=40),
        )
        charts_data["units_lost"] = fig3

# Create the draggable charts dashboard
with elements(f"chart_dashboard_{st.session_state.layout_timestamp}"):
    # Force the layout to be used by creating a new copy
    current_chart_layout = copy.deepcopy(st.session_state.temp_chart_layout)
    print("Current chart layout being used:", current_chart_layout)
    
    # Add custom CSS for white resize handles directly in the elements context
    html.Style("""
    .react-resizable-handle {
        filter: invert(1) !important;
        opacity: 0.7 !important;
    }
    .react-resizable-handle:hover {
        opacity: 1 !important;
    }
    .toggle-switch {
        display: flex;
        align-items: center;
        padding: 0px 16px 8px 16px;
    }
    .toggle-label {
        margin-right: 8px;
        color: white;
        font-size: 14px;
    }
    """)
    
    with dashboard.Grid(
        layout=current_chart_layout,
        ncols=12,
        draggableHandle=".draggable-chart-header",
        onLayoutChange=update_chart_layout,
        rowHeight=70,
    ):
        # Add charts to the dashboard
        for chart_id, fig in charts_data.items():
            # Skip the toggle flag
            if chart_id == "units_comparison_toggle":
                continue
                
            # Base styling for all charts
            fig.update_layout(
                paper_bgcolor='rgba(0,0,0,0)',
                plot_bgcolor='rgba(0,0,0,0)',
                font={"color": "white"},
                height=500,
                hoverlabel=dict(
                    font_color="white",
                    font_size=12,
                    bordercolor="rgba(255, 255, 255, 0.3)"
                )
            )
            
            # Make grid lines light gray
            fig.update_xaxes(
                showgrid=True, 
                gridwidth=1, 
                gridcolor='rgba(211, 211, 211, 0.3)'
            )
            fig.update_yaxes(
                showgrid=True, 
                gridwidth=1, 
                gridcolor='rgba(211, 211, 211, 0.3)'
            )
            
            # Get the appropriate title based on chart_id
            chart_titles = {
                "loss_trend": "Distribution Loss Percentage by Month (Jan-Mar 2025)",
                "units_comparison": "Units Received vs Sold" + (" (All Months)" if month == "All" else f" ({month} 2025)"),
                "units_lost": "Total Units Lost Trend"
            }
            
            with mui.Paper(key=chart_id, sx={
                "display": "flex", 
                "flexDirection": "column", 
                "height": "100%", 
                "overflow": "hidden",
                "borderRadius": "10px",
                "boxShadow": "0 4px 6px rgba(0, 0, 0, 0.1)",
                "background": "transparent"
            }):
                # Standard header for all charts (without toggle)
                mui.Box(
                    className="draggable-chart-header",
                    sx={
                        "padding": "8px 16px",
                        "backgroundColor": "transparent",
                        "cursor": "move",
                        "borderTopLeftRadius": "10px",
                        "borderTopRightRadius": "10px"
                    },
                    children=[
                        mui.Typography(chart_titles[chart_id], variant="h6", sx={"color": "white", "fontSize": "16px"})
                    ]
                )
                
                # Convert to HTML and embed
                html_str = fig.to_html(
                    include_plotlyjs="cdn",
                    full_html=False,
                    config={
                        'displayModeBar': True,
                        'responsive': True,
                        'scrollZoom': False
                    }
                )
                
                html.Iframe(
                    srcDoc=html_str,
                    style={
                        "border": "none", 
                        "width": "100%", 
                        "height": "100%", 
                        "flexGrow": 1,
                        "backgroundColor": "transparent"
                    },
                )

# Analysis Section
st.markdown("## Feeder Analysis")

# Function to save tables layout changes
def save_tables_layout(layout):
    st.session_state.tables_layout = layout
    if "saved_tables_layout" in st.session_state:
        st.session_state.saved_tables_layout = layout.copy()

# Prepare data for tables
tables_data = {}

# High Loss Feeders Table
if month == "All":
    # Calculate average loss percentage across all months for each feeder
    avg_loss = filtered_df.copy()
    avg_loss["Avg Loss %"] = (
        (avg_loss["Units Lost Jan-25"] + avg_loss["Units Lost Feb-25"] + avg_loss["Units Lost Mar-25"]) / 
        (avg_loss["Units Received Jan-25"] + avg_loss["Units Received Feb-25"] + avg_loss["Units Received Mar-25"]) * 100
    )
    high_loss = avg_loss.sort_values("Avg Loss %", ascending=False).head(5)
    high_loss_table = high_loss[["Feeder Name", "Avg Loss %"]].copy()
    high_loss_table.columns = ["Feeder", "Loss Percentage (%)"]
    tables_data["high_loss"] = {
        "title": "Top 5 Feeders with Highest Loss % (All Months)",
        "data": high_loss_table
    }
else:
    loss_col = f"Distribution Loss %age {month}-25"
    high_loss = filtered_df.sort_values(loss_col, ascending=False).head(5)
    high_loss_table = high_loss[["Feeder Name", loss_col]].copy()
    high_loss_table.columns = ["Feeder", "Loss Percentage (%)"]
    tables_data["high_loss"] = {
        "title": f"Top 5 Feeders with Highest Loss % ({month})",
        "data": high_loss_table
    }

# Low Loss Feeders Table
if month == "All":
    # Use the previously calculated average loss
    low_loss = avg_loss[avg_loss["Avg Loss %"] > 0].sort_values("Avg Loss %").head(5)
    low_loss_table = low_loss[["Feeder Name", "Avg Loss %"]].copy()
    low_loss_table.columns = ["Feeder", "Loss Percentage (%)"]
    tables_data["low_loss"] = {
        "title": "Top 5 Feeders with Lowest Loss % (All Months)",
        "data": low_loss_table
    }
else:
    loss_col = f"Distribution Loss %age {month}-25"
    low_loss = filtered_df[filtered_df[loss_col] > 0].sort_values(loss_col).head(5)
    low_loss_table = low_loss[["Feeder Name", loss_col]].copy()
    low_loss_table.columns = ["Feeder", "Loss Percentage (%)"]
    tables_data["low_loss"] = {
        "title": f"Top 5 Feeders with Lowest Loss % ({month})",
        "data": low_loss_table
    }

# Negative Loss Feeders Table
if month == "All":
    # Filter for negative values in the average loss
    negative_loss = avg_loss[avg_loss["Avg Loss %"] < 0].sort_values("Avg Loss %")
    if not negative_loss.empty:
        negative_loss_table = negative_loss[["Feeder Name", "Avg Loss %"]].copy()
        negative_loss_table.columns = ["Feeder", "Loss Percentage (%)"]
        tables_data["negative_loss"] = {
            "title": "Feeders with Negative Loss % (All Months)",
            "data": negative_loss_table
        }
    else:
        tables_data["negative_loss"] = {
            "title": "Feeders with Negative Loss % (All Months)",
            "data": pd.DataFrame({"Feeder": ["No feeders with negative loss found"], "Loss Percentage (%)": [0]})
        }
else:
    loss_col = f"Distribution Loss %age {month}-25"
    negative_loss = filtered_df[filtered_df[loss_col] < 0].sort_values(loss_col)
    if not negative_loss.empty:
        negative_loss_table = negative_loss[["Feeder Name", loss_col]].copy()
        negative_loss_table.columns = ["Feeder", "Loss Percentage (%)"]
        tables_data["negative_loss"] = {
            "title": f"Feeders with Negative Loss % ({month})",
            "data": negative_loss_table
        }
    else:
        tables_data["negative_loss"] = {
            "title": f"Feeders with Negative Loss % ({month})",
            "data": pd.DataFrame({"Feeder": [f"No feeders with negative loss found for {month}"], "Loss Percentage (%)": [0]})
        }

# Create the draggable tables dashboard
with elements(f"tables_dashboard_{st.session_state.layout_timestamp}"):
    # Force the layout to be used by creating a new copy
    current_tables_layout = copy.deepcopy(st.session_state.temp_tables_layout)
    print("Current tables layout being used:", current_tables_layout)
    
    with dashboard.Grid(
        layout=current_tables_layout,
        ncols=12,
        draggableHandle=".draggable-table-header",
        onLayoutChange=update_tables_layout,
        rowHeight=70,
    ):
        # Add tables to the dashboard
        for table_id, table_info in tables_data.items():
            with mui.Paper(key=table_id, sx={
                "display": "flex", 
                "flexDirection": "column", 
                "height": "100%", 
                "overflow": "hidden",
                "borderRadius": "10px",
                "boxShadow": "0 4px 6px rgba(0, 0, 0, 0.1)",
                "background": "transparent"
            }):
                mui.Box(
                    className="draggable-table-header",
                    sx={
                        "padding": "8px 16px",
                        "backgroundColor": "rgba(0, 0, 0, 0.2)",
                        "cursor": "move",
                        "borderTopLeftRadius": "10px",
                        "borderTopRightRadius": "10px"
                    },
                    children=[
                        mui.Typography(table_info["title"], variant="h6", sx={"color": "white", "fontSize": "16px"})
                    ]
                )
                
                # Convert DataFrame to HTML table with styling
                table_html = table_info["data"].to_html(
                    index=False,
                    classes=["styled-table"],
                    border=0
                )
                
                # Add custom CSS for the table
                styled_table_html = f"""
                <style>
                .styled-table {{
                    width: 100%;
                    border-collapse: collapse;
                    font-family: 'Roboto', sans-serif;
                    color: white;
                }}
                .styled-table th {{
                    background-color: rgba(0, 0, 0, 0.2);
                    padding: 8px 12px;
                    text-align: left;
                    font-weight: 500;
                }}
                .styled-table td {{
                    padding: 8px 12px;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                }}
                .styled-table tr:hover {{
                    background-color: rgba(255, 255, 255, 0.05);
                }}
                </style>
                {table_html}
                """
                
                html.Iframe(
                    srcDoc=styled_table_html,
                    style={
                        "border": "none", 
                        "width": "100%", 
                        "height": "100%", 
                        "flexGrow": 1,
                        "backgroundColor": "transparent"
                    },
                )

# Add a drop area with instructions
st.markdown("""
<div style="border: 2px dashed rgba(255, 255, 255, 0.3); border-radius: 10px; padding: 20px; text-align: center; margin-bottom: 20px;">
    <p style="color: rgba(255, 255, 255, 0.7); font-size: 16px;">
        <i>To add a custom chart, select a chart type from the sidebar and click the "Add (Chart Type)" button below it.</i>
    </p>
</div>
""", unsafe_allow_html=True)

# Data selection modal
if "show_data_modal" in st.session_state and st.session_state.show_data_modal:
    chart_type = st.session_state.current_chart_type
    
    with st.form(key="chart_data_form"):
        st.subheader(f"Configure {chart_type}")
        
        # Get available columns from the data
        numeric_cols = [col for col in df.columns if pd.api.types.is_numeric_dtype(df[col])]
        categorical_cols = [col for col in df.columns if not pd.api.types.is_numeric_dtype(df[col])]
        
        # Different options based on chart type
        if chart_type in ["Line Chart", "Bar Chart", "Scatter Plot"]:
            x_axis = st.selectbox("X-Axis", options=df.columns)
            y_axis = st.selectbox("Y-Axis", options=numeric_cols)
            
            if chart_type in ["Line Chart", "Bar Chart"]:
                group_by = st.selectbox("Group By (Optional)", options=["None"] + categorical_cols)
            else:  # Scatter Plot
                color_by = st.selectbox("Color By (Optional)", options=["None"] + categorical_cols)
                size_by = st.selectbox("Size By (Optional)", options=["None"] + numeric_cols)
        
        elif chart_type == "Pie Chart":
            labels = st.selectbox("Labels", options=categorical_cols)
            values = st.selectbox("Values", options=numeric_cols)
        
        elif chart_type == "Heatmap":
            x_axis = st.selectbox("X-Axis", options=categorical_cols)
            y_axis = st.selectbox("Y-Axis", options=categorical_cols)
            values = st.selectbox("Values", options=numeric_cols)
            color_scale = st.selectbox("Color Scale", options=["Viridis", "Plasma", "Inferno", "Magma", "Cividis", "Turbo"])
        
        # Common options for all charts
        chart_title = st.text_input("Chart Title", value=chart_type)
        
        # Submit button
        submitted = st.form_submit_button("Add Chart")
        cancel = st.form_submit_button("Cancel")
        
        if submitted:
            # Create chart configuration based on selections
            chart_config = {
                "type": chart_type.lower().split()[0],  # Extract first word (line, bar, pie, etc.)
                "title": chart_title,
                "id": f"custom_{chart_type.lower().replace(' ', '_')}_{len(st.session_state.custom_charts)}",
            }
            
            # Add chart-specific configurations
            if chart_type in ["Line Chart", "Bar Chart"]:
                chart_config.update({
                    "x_axis": x_axis,
                    "y_axis": y_axis,
                    "group_by": group_by if group_by != "None" else None
                })
            elif chart_type == "Pie Chart":
                chart_config.update({
                    "labels": labels,
                    "values": values
                })
            elif chart_type == "Heatmap":
                chart_config.update({
                    "x_axis": x_axis,
                    "y_axis": y_axis,
                    "values": values,
                    "color_scale": color_scale
                })
            
            # Add the chart to the custom charts list
            st.session_state.custom_charts.append(chart_config)
            
            # Close the modal
            st.session_state.show_data_modal = False
            st.rerun()
        
        if cancel:
            st.session_state.show_data_modal = False
            st.rerun()

# Initialize custom charts in session state if not present
if "custom_charts" not in st.session_state:
    st.session_state.custom_charts = []

# Add a function to generate custom charts based on configuration
def generate_custom_chart(chart_config, filtered_df):
    """Generate a custom chart based on the provided configuration"""
    chart_type = chart_config["type"]
    title = chart_config["title"]
    
    if chart_type == "line":
        x_axis = chart_config["x_axis"]
        y_axis = chart_config["y_axis"]
        group_by = chart_config.get("group_by")
        
        if group_by:
            # Create line chart with grouping
            fig = px.line(
                filtered_df,
                x=x_axis,
                y=y_axis,
                color=group_by,
                title="",  # Remove internal title
                markers=True,
                color_discrete_sequence=px.colors.qualitative.Plotly  # Use Plotly's vibrant colors
            )
        else:
            # Create simple line chart
            fig = px.line(
                filtered_df,
                x=x_axis,
                y=y_axis,
                title="",  # Remove internal title
                markers=True,
                color_discrete_sequence=['#636EFA']  # Use blue color
            )
    
    elif chart_type == "bar":
        x_axis = chart_config["x_axis"]
        y_axis = chart_config["y_axis"]
        group_by = chart_config.get("group_by")
        
        if group_by:
            # Create bar chart with grouping
            fig = px.bar(
                filtered_df,
                x=x_axis,
                y=y_axis,
                color=group_by,
                title="",  # Remove internal title
                barmode="group",
                color_discrete_sequence=px.colors.qualitative.Plotly  # Use Plotly's vibrant colors
            )
        else:
            # Create simple bar chart
            fig = px.bar(
                filtered_df,
                x=x_axis,
                y=y_axis,
                title="",  # Remove internal title
                color_discrete_sequence=['#636EFA']  # Use blue color
            )
    
    elif chart_type == "pie":
        labels = chart_config["labels"]
        values = chart_config["values"]
        
        # Group by labels and sum values
        pie_data = filtered_df.groupby(labels)[values].sum().reset_index()
        
        # Create pie chart
        fig = px.pie(
            pie_data,
            names=labels,
            values=values,
            title="",  # Remove internal title
            hole=0.4,
            color_discrete_sequence=px.colors.qualitative.Plotly  # Use Plotly's vibrant colors
        )
    
    elif chart_type == "heatmap":
        x_axis = chart_config["x_axis"]
        y_axis = chart_config["y_axis"]
        values = chart_config["values"]
        color_scale = chart_config.get("color_scale", "Viridis")
        
        # Pivot data for heatmap
        pivot_data = filtered_df.pivot_table(
            index=y_axis,
            columns=x_axis,
            values=values,
            aggfunc='mean'
        ).fillna(0)
        
        # Create heatmap
        fig = px.imshow(
            pivot_data,
            labels=dict(x=x_axis, y=y_axis, color=values),
            x=pivot_data.columns,
            y=pivot_data.index,
            color_continuous_scale=color_scale,
            title=""  # Remove internal title
        )
    
    else:
        # Default empty chart if type not recognized
        fig = px.scatter(title="Unsupported chart type")
    
    # Apply common styling to match predefined charts
    fig.update_layout(
        paper_bgcolor='rgba(0,0,0,0)',
        plot_bgcolor='rgba(0,0,0,0)',
        font={"color": "white"},
        height=500,
        hoverlabel=dict(
            font_color="white",
            font_size=12,
            bordercolor="rgba(255, 255, 255, 0.3)"
        ),
        margin=dict(l=40, r=40, t=40, b=40)
    )
    
    # Make grid lines light gray
    fig.update_xaxes(
        showgrid=True, 
        gridwidth=1, 
        gridcolor='rgba(211, 211, 211, 0.3)'
    )
    fig.update_yaxes(
        showgrid=True, 
        gridwidth=1, 
        gridcolor='rgba(211, 211, 211, 0.3)'
    )
    
    return fig

# Display custom charts if any exist
if st.session_state.custom_charts:
    st.markdown("<h4 style='color: #5c6984; margin-top: 30px;'>Custom Visualizations</h4>", unsafe_allow_html=True)
    
    # Define default layout for custom charts
    if "custom_chart_layout" not in st.session_state:
        st.session_state.custom_chart_layout = [
            {"i": chart["id"], "x": i % 2 * 6, "y": (i // 2) * 8, "w": 6, "h": 8, "minW": 3, "minH": 4}
            for i, chart in enumerate(st.session_state.custom_charts)
        ]
    
    # Update layout if number of charts changed
    if len(st.session_state.custom_chart_layout) != len(st.session_state.custom_charts):
        # Add layout for new charts
        current_ids = [item["i"] for item in st.session_state.custom_chart_layout]
        for chart in st.session_state.custom_charts:
            if chart["id"] not in current_ids:
                # Place new chart at the end
                next_y = max([item["y"] for item in st.session_state.custom_chart_layout], default=0) + 8
                st.session_state.custom_chart_layout.append({
                    "i": chart["id"], 
                    "x": 0, 
                    "y": next_y, 
                    "w": 6, 
                    "h": 8
                })
    
    # Function to update custom chart layout
    def update_custom_chart_layout(layout):
        st.session_state.custom_chart_layout = layout
    
    # Function to remove a chart
    def remove_custom_chart(chart_id):
        # Remove the chart from the list
        st.session_state.custom_charts = [
            chart for chart in st.session_state.custom_charts if chart["id"] != chart_id
        ]
        # Remove the chart from the layout
        st.session_state.custom_chart_layout = [
            item for item in st.session_state.custom_chart_layout if item["i"] != chart_id
        ]
        # Force a rerun to update the UI
        st.rerun()
    
    # Add a section for managing custom charts
    if st.session_state.custom_charts:
        st.markdown("<h5 style='color: #5c6984; margin-top: 10px;'>Manage Custom Charts</h5>", unsafe_allow_html=True)
        
        # Create columns for the remove buttons
        cols = st.columns(4)
        
        # Add a remove button for each chart
        for i, chart in enumerate(st.session_state.custom_charts):
            col_idx = i % 4
            with cols[col_idx]:
                if st.button(f"Remove: {chart['title']}", key=f"remove_{chart['id']}"):
                    remove_custom_chart(chart["id"])
    
    # Create the draggable custom charts dashboard
    with elements(f"custom_charts_dashboard_{st.session_state.layout_timestamp}"):
        # Add the CSS inside the elements context
        html.Style("""
        .react-resizable-handle {
            filter: invert(1) !important;
            opacity: 0.7 !important;
        }
        
        .react-resizable-handle:hover {
            opacity: 1 !important;
        }
        """)
        
        # Force the layout to be used by creating a new copy
        current_custom_layout = copy.deepcopy(st.session_state.custom_chart_layout)
        
        with dashboard.Grid(
            layout=current_custom_layout,
            ncols=12,
            draggableHandle=".draggable-custom-header",
            onLayoutChange=update_custom_chart_layout,
            rowHeight=70,
            compactType="vertical",
            margin=[20, 20],
            containerPadding=[20, 20],
            isResizable=True
        ):
            # Add charts to the dashboard
            for chart in st.session_state.custom_charts:
                with mui.Paper(key=chart["id"], sx={
                    "display": "flex", 
                    "flexDirection": "column", 
                    "height": "100%", 
                    "overflow": "hidden",
                    "borderRadius": "10px",
                    "boxShadow": "0 4px 6px rgba(0, 0, 0, 0.1)",
                    "background": "transparent",
                    "border": "none"
                }):
                    # Header with title and remove button
                    mui.Box(
                        className="draggable-custom-header",
                        sx={
                            "padding": "8px 16px",
                            "backgroundColor": "transparent",
                            "cursor": "move",
                            "borderTopLeftRadius": "10px",
                            "borderTopRightRadius": "10px",
                            "display": "flex",
                            "justifyContent": "space-between",
                            "alignItems": "center"
                        },
                        children=[
                            mui.Typography(
                                chart["title"],
                                variant="h6", 
                                sx={"color": "white", "fontSize": "16px"}
                            ),
                            mui.IconButton(
                                children=mui.icon.Close,
                                onClick=lazy(lambda: remove_custom_chart(chart["id"])),
                                sx={"color": "white", "padding": "4px"}
                            )
                        ]
                    )
                    
                    # Generate chart based on configuration
                    fig = generate_custom_chart(chart, filtered_df)
                    
                    # Convert to HTML and embed
                    html_str = fig.to_html(
                        include_plotlyjs="cdn",
                        full_html=False,
                        config={
                            'displayModeBar': True,
                            'responsive': True,
                            'scrollZoom': False
                        }
                    )
                    
                    html.Iframe(
                        srcDoc=html_str,
                        style={
                            "border": "none", 
                            "width": "100%", 
                            "height": "100%", 
                            "flexGrow": 1,
                            "backgroundColor": "transparent",
                            "minHeight": "300px"
                        },
                    )

# Footer
st.markdown("---")
st.markdown("Data source: FESCO Distribution Data | Last updated: April 2025")
