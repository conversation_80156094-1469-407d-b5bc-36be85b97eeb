import os
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.staticfiles import Static<PERSON>iles
from fastapi.templating import <PERSON><PERSON>2Templates
from pathlib import Path
from utils.config import BASE_DIR, OUTPUT_DIR

def create_app():
    # Create FastAPI app
    app = FastAPI(title="ThunderBot⚡", description="A GenAI assistant for telecom-energy")
    
    # Ensure output directory exists
    Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)
    
    # Setup templates
    app.templates = Jinja2Templates(directory=os.path.join(BASE_DIR, "templates"))
    
    # Create static directory if it doesn't exist
    static_dir = os.path.join(BASE_DIR, "static")
    Path(static_dir).mkdir(parents=True, exist_ok=True)
    
    # Mount static files with absolute path
    app.mount("/static", StaticFiles(directory=static_dir), name="static")
    
    # Register routes
    from api.routes import register_routes
    register_routes(app)
    
    return app

if __name__ == '__main__':
    import uvicorn
    import sys
    import os
    
    # Add the parent directory to sys.path to make imports work
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    
    app = create_app()
    port = int(os.environ.get('PORT', 5013))
    debug = os.environ.get('FASTAPI_DEBUG', 'True').lower() == 'true'
    
    if debug:
        # For development with hot reload
        print("Starting development server with hot reload...")
        # Run directly with uvicorn
        uvicorn.run(
            "app.main:create_app", 
            factory=True,
            host="0.0.0.0", 
            port=port, 
            reload=True,
            reload_dirs=[os.path.dirname(os.path.abspath(__file__))]
        )
    else:
        # For production
        uvicorn.run(app, host="0.0.0.0", port=port)


