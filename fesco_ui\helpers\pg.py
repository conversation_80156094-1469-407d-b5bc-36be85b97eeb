import json
import os
import sys
import time
import sys,subprocess

try:
    import pandas as pd
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", 'pandas'])
finally:
    import pandas as pd

try:    
    import psycopg2
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", 'psycopg2-binary'])
finally:
    import psycopg2

try:
    import sqlalchemy
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", 'sqlalchemy'])
finally:
    import sqlalchemy

from helpers.Methods.func import read_config, timing_decorator
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import LargeBinary, types, TIMESTAMP
import traceback
from sqlalchemy.dialects.postgresql import UUID, JSONB



class database():
    def __init__(self,dir,filename="postgressDB_config.json"):
        self.base_dir   =   dir
        self.filename   =   filename
        
    @timing_decorator(decimal_points=2, delay_unit='s')
    def db_connections(self, db_config):
        self.db_config  =   read_config(db_config)     
        connections     =   []
        connection_type =   self.db_config.get("connection")

        if connection_type in ["alchemy", "both"]:
            try:
                engine = sqlalchemy.create_engine(
                    f"postgresql+psycopg2://{self.db_config['user']}:{self.db_config['password']}@"
                    f"{self.db_config['host']}:{self.db_config['port']}/{self.db_config['dbname']}"
                )
                connections.append(engine.connect())
            except (SQLAlchemyError,ConnectionRefusedError, ConnectionError, ConnectionAbortedError) as e:
                print(f"SQL Alchemy Connection Failed: {e}")
                return None

        if connection_type in ["pg", "both"]:
            try:
                connection_pg   =   psycopg2.connect(
                    user        =   self.db_config["user"],
                    password    =   self.db_config["password"],
                    host        =   str(self.db_config["host"]),
                    port        =   str(self.db_config["port"]),
                    database    =   self.db_config["dbname"]
                                                    )
                connection_pg.autocommit    =   True
                connections.append(connection_pg)
            except (psycopg2.OperationalError, ConnectionRefusedError, ConnectionError, ConnectionAbortedError) as e:
                print(f"psycopg2 Connection Failed: {e}")
                return None
        return connections
    
    def read_sql_query(self, query, max_retries=3, retry_interval=5, **kwargs):
        pg,_ = self.reconnect(None,None)
        with pg.cursor() as cursor:
            for _ in range(max_retries):             
                try:
                    cursor.execute(query)
                    df = [list(row) for row in cursor.fetchall()]
                    return None if df is None else df
                except (psycopg2.Error, pd.errors.DatabaseError) as e:
                    print(f"Error executing SQL query: {e}")
                time.sleep(retry_interval)
            print(f"Max retries exceeded. Unable to execute SQL query: {query}")
        pg.close()
        return None
    
    @timing_decorator(decimal_points=2, delay_unit='s')
    def read_sql_query_to_df(self, query, max_retries=3, retry_interval=5, **kwargs):
        pg, _ = self.reconnect(None, None)
        with pg.cursor() as cursor:
            for _ in range(max_retries):
                try:
                    cursor.execute(query)
                    # Use list comprehension to fetch all rows as a list of tuples
                    rows = [row for row in cursor.fetchall()]
                    
                    # Use the column names from the cursor description
                    columns = [desc[0] for desc in cursor.description]
                    
                    # Create a Pandas DataFrame using the rows and columns
                    df = pd.DataFrame(rows, columns=columns)
                    
                    return df
                except (psycopg2.Error, pd.errors.DatabaseError) as e:
                    print(f"Error executing SQL query: {e}")
                    
                time.sleep(retry_interval)
            
            print(f"Max retries exceeded. Unable to execute SQL query: {query}")

        pg.close()
        return None
    


    def reconnect(self,pg, db_alchemy):
        file_path   =   os.path.join(self.base_dir, 'config',self.filename)
        db_conn     =   database(self.base_dir).db_connections(file_path)
        try:
            if len(db_conn) == 2:
                if isinstance(db_conn[0], sqlalchemy.engine.Connection) and isinstance(db_conn[1], psycopg2.extensions.connection):
                    db_alchemy, db_pg = db_conn
                    return db_pg, db_alchemy
                else:
                    sys.exit("Unable to Connect to PostgreSQL Database [Check Your Database Configurations]")
            elif len(db_conn) == 1:
                if isinstance(db_conn[0], sqlalchemy.engine.Connection):
                    db_alchemy = db_conn[0]
                    return db_pg, db_alchemy
                else:
                    db_pg = db_conn[0]
                    return db_pg, db_alchemy
            else:
                sys.exit("Unable to Connect to PostgreSQL Database [Check Your Database Configurations]")
        except Exception as e:
            print(f"Error in reconnect : {e}")
            return None, None

        

    def get_table_column_types(self, table):
        def replace_dictionary_values(dict1, dict2):
            updated_dict = {key: dict2[value] if value in dict2 else value for key, value in dict1.items()}
            return updated_dict
        pg,_ = self.reconnect(None,None)
        with pg.cursor() as cursor:
            query = "SELECT column_name, data_type FROM information_schema.columns WHERE table_name = %s"
            
            cursor.execute(query, (table,))
            column_types = cursor.fetchall()

        # Create a dictionary to store column names and data types
        column_dict = {}
        for column in column_types:
            column_name, data_type = column
            column_dict[column_name] = data_type
        
        sqlalchemy_types = {
                'integer': types.Integer,
                'bigint': types.BigInteger,
                'smallint': types.SmallInteger,
                'numeric': types.Numeric,
                'real': types.Float,
                'double precision': types.Float,
                'varchar': types.String,
                'character varying':types.String,
                'char': types.String,
                'text': types.Text,
                'date': types.Date,
                'time without time zone': types.Time,
                'timestamp without time zone': types.TIMESTAMP,
                'timestamp with time zone': TIMESTAMP(timezone=True),
                'boolean': types.Boolean,
                'uuid': UUID,
                'bytea': LargeBinary,
                'jsonb': JSONB
            }
        
        column_dict = replace_dictionary_values(column_dict, sqlalchemy_types)
        pg.close()
        return column_dict

    @timing_decorator(decimal_points=2, delay_unit='s')
    def load_data_to_db(self, table_name, df, col_types,temptable):
        # Check Connection        
        pg, alchemy = self.reconnect( None, None)

        # Define a function to create the temporary table
        def create_temp_table(df, alchemy, col_types,pg):
            dummy_df = pd.DataFrame(columns=col_types.keys())

            dummy_df.to_sql(temptable, alchemy, if_exists='replace', index=False, dtype=col_types)    
            # query = "SELECT create_hypertable(%s, %s,chunk_time_interval => INTERVAL '1 day')"

            # pg.cursor().execute(query, ("temp_abb", "unixtime"))  
            # pg.commit()
            df.to_sql(temptable, alchemy, if_exists='append', index=False,dtype=col_types)


        # Define a function to execute the upsert query
        def execute_upsert_query(table_name, pg, df):
            with pg.cursor() as cur:
                # Common set clause for update 
                if table_name==""" "Hardware" """ or table_name == """ "Region" """ or table_name==""" "Cluster" """ or table_name==""" "District" """ or table_name==""" "Site" """:
                    set_clause = ', '.join([f""""{col}" = tr."{col}" """ for col in df.columns])
                else:
                    set_clause = ', '.join([f""""{col}" = tr.{col}""" for col in df.columns])

                # Update query
                start_time = time.time()
                update_query = f"""
                    UPDATE {table_name} t
                    SET {set_clause}
                    FROM {temptable} tr
                    WHERE {get_condition(table_name)}
                """
                cur.execute(update_query)
                print(f"---> Update Query Done in {round(time.time() - start_time, 2)}")

                # Insert query
                start_time = time.time()
                
                if table_name==""" "Hardware" """ or table_name == """ "Region" """ or table_name==""" "Cluster" """ or table_name==""" "District" """ or table_name==""" "Site" """:
                    insert= ', '.join([f""""{col}" """ for col in df.columns])
                    insert_query = f"""
                                        INSERT INTO {table_name} ({insert})
                                        SELECT {insert} FROM {temptable} tr
                                        WHERE NOT EXISTS (SELECT 1 FROM {table_name} t WHERE {get_condition(table_name)})
                                    """
                else:
                    insert_query = f"""
                                        INSERT INTO {table_name} ({', '.join(df.columns)})
                                        SELECT {', '.join(df.columns)} FROM {temptable} tr
                                        WHERE NOT EXISTS (SELECT 1 FROM {table_name} t WHERE {get_condition(table_name)})
                                    """
                
                cur.execute(insert_query)
                print(f"---> Insert Query Done in {round(time.time() - start_time, 2)}")

                # Drop Temp Table
                cur.execute(f"""DROP TABLE IF EXISTS {temptable}""")
                pg.commit()
                print(f"{table_name} data loaded successfully.")

        def get_condition(table_name):
            if table_name == 'messagesalert':
                return "t.time = tr.time and t.displaypoint = tr.displaypoint and t.stringvalue = tr.stringvalue and t.hwcode = tr.hwcode"
            elif table_name in ['hourly','hourly_old','daily']:
                return "t.timeinterval = tr.timeinterval and t.sitecode = tr.sitecode and t.sitecondition = tr.sitecondition"
            elif table_name in ['monthly']:
                return "t.timeinterval = tr.timeinterval and t.sitecode = tr.sitecode"
            elif table_name in ['thunderbotlogs']:
                return "t.userid = tr.userid and t.sessionid = tr.sessionid"
            else:
                return "t.timeinterval = tr.timeinterval and t.sitecode = tr.sitecode"

        try:
            start_time = time.time()
            create_temp_table(df, alchemy, col_types,pg)
            print(f"---> Temp Data Inserted in {round(time.time() - start_time,2)}")
        except Exception as e:
            print(f"Except to_sql : {e}")
            raise

        try:
            execute_upsert_query(table_name, pg, df)
        except Exception as e:
            print(f"Database Upsert Error : {e}")
            pg.rollback()
            raise
        


    def read_schema(self,table_name):
        pg,_ = self.reconnect(None,None)
        with pg.cursor() as cursor:
            query       =   f"""SELECT column_name, data_type FROM information_schema.columns WHERE table_name = '{table_name}'"""
            cursor.execute(query)
            df = pd.DataFrame(cursor.fetchall(), columns=['column','dtype'])    
            if df is None:
                pg.close()
                return None
            else:
                pg.close()
                return df
        
        
    def append_data(self,table_name,df, col_types):
        try:
            print("----------->Pushing Data")
            _,alchemy = self.reconnect(None,None)
            df.to_sql(table_name, alchemy, if_exists='append', index=False,dtype=col_types)
            print("----------->Data Pushed !")
        except Exception as e:
            print("Error While Pushing Data ",e)
        


    def run_query(self,query):
        pg,_ = self.reconnect(None,None)
        if pg:
            try:
                with pg.cursor() as cursor:
                        cursor.execute(query)
                        pg.commit()
                        print(" Query Done. ")
                        print("Message:", cursor.statusmessage)
            except Exception as e:
                print(f"Database Query Error : {e}")
                # pg.rollback()
                raise