import time
import asyncio
from typing import Optional
from utils.api_key_manager import  APIKeyManager
from utils.config import API_KEYS, DEFAULT_MODEL_NAME
import google.generativeai as genai

global_key_manager      =   APIKeyManager(
                                api_keys=API_KEYS,
                                requests_per_minute=15
                            )
def load_model():
    total_retries = 0
    max_total_retries = 3

    while total_retries < max_total_retries:
        try:

            api_key                 =   global_key_manager.get_next_available_key()
            print(api_key)
            genai.configure(api_key=api_key)
            model                   =   genai.GenerativeModel(DEFAULT_MODEL_NAME)
            return model
        except Exception as e:
            error_message = str(e)
            total_retries += 1

            print(f"\nError with key {api_key[:10]}: {error_message}")
            global_key_manager.mark_key_error(api_key, error_message)

            if total_retries >= max_total_retries:
                raise Exception(f"Max retries ({max_total_retries}) exceeded. Last error: {error_message}")

            time.sleep(0.1)
            continue

  

async def call_gemini(prompt, max_tokens=512, temperature=0.7):
    """
    Call the Gemini model with automatic key rotation and retry logic
    Now fully async
    """
    total_retries = 0
    max_total_retries = 5
    last_api_key = None
    
    while total_retries < max_total_retries:
        try:
            # Load model is still synchronous, so we need to run it in a thread
            model = await asyncio.to_thread(load_model)
            last_api_key = global_key_manager.current_api_key
            
            # Generate content is synchronous, so we need to run it in a thread
            response = await asyncio.to_thread(
                lambda: model.generate_content(
                    prompt,
                    generation_config={
                        "max_output_tokens": max_tokens,
                        "temperature": temperature
                    }
                )
            )
            
            # Mark the key as successful
            if last_api_key:
                global_key_manager.mark_key_success(last_api_key)
            
            # Return the text response
            return response.text
            
        except Exception as e:
            error_message = str(e)
            total_retries += 1
            
            if last_api_key:
                print(f"\nError with key {last_api_key[:10]}...: {error_message}")
                global_key_manager.mark_key_error(last_api_key, error_message)
            else:
                print(f"\nError with unknown key: {error_message}")
            
            if total_retries >= max_total_retries:
                raise Exception(f"Max retries ({max_total_retries}) exceeded. Last error: {error_message}")
            
            await asyncio.sleep(0.5)  # Use asyncio.sleep instead of time.sleep
            continue





