import asyncio
import pandas as pd
import sys
import os
from pathlib import Path

# Add the parent directory to sys.path to import from app
sys.path.append(str(Path(__file__).parent.parent.parent))

from app.services.analysis_service import AnalysisService

async def test_analysis_service():
    """Test the Analysis Service with sample data"""
    print("=== Testing Analysis Service ===")
    
    # Create a sample dataframe
    data = {
        'site_id': ['SITE001', 'SITE002', 'SITE003', 'SITE004', 'SITE005'],
        'region': ['North', 'South', 'East', 'West', 'North'],
        'power_usage_kw': [120.5, 95.2, 150.8, 110.3, 135.7],
        'uptime_percent': [99.8, 98.5, 97.2, 99.5, 99.9],
        'maintenance_cost': [5000, 7500, 6200, 4800, 5500]
    }
    
    df = pd.DataFrame(data)
    print("\nSample DataFrame:")
    print(df.head())
    
    # Create a sample user query
    user_query = "What are the power usage trends across different regions?"
    
    # Initialize the Analysis Service
    analysis_service = AnalysisService()
    
    # Test the analysis
    print("\nGenerating analysis...")
    async for result in analysis_service.handle_analysis(df, user_query):
        print("\nAnalysis Result:")
        print(f"Type: {result['type']}")
        print(f"Content: {result['content']}")
    
    # Test with a different type of data
    print("\n=== Testing with Time Series Data ===")
    
    # Create a time series dataframe
    time_data = {
        'date': pd.date_range(start='2023-01-01', periods=10, freq='D'),
        'traffic_gb': [250, 275, 290, 310, 305, 270, 260, 300, 330, 350],
        'latency_ms': [15, 18, 14, 16, 19, 17, 15, 14, 16, 18]
    }
    
    time_df = pd.DataFrame(time_data)
    print("\nTime Series DataFrame:")
    print(time_df.head())
    
    # Create a sample user query for time series
    time_query = "How has network traffic and latency changed over time?"
    
    # Test the analysis with time series data
    print("\nGenerating time series analysis...")
    async for result in analysis_service.handle_analysis(time_df, time_query):
        print("\nTime Series Analysis Result:")
        print(f"Type: {result['type']}")
        print(f"Content: {result['content']}")

if __name__ == "__main__":
    # Run the test
    asyncio.run(test_analysis_service())