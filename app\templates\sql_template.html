<!DOCTYPE html>
<html>
<head>
    <title>SQL Analysis</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Add Plotly.js for visualization -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        .result-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .query-section {
            background-color: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        #visualization {
            width: 100%;
            height: 500px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        .text-response {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Query Results</h1>
        
        <div class="mb-4">
            <h3>Your Query</h3>
            <p id="originalQuery"></p>
        </div>

        <!-- Add text responses section -->
        <div class="result-section">
            <h3>Analysis</h3>
            <div id="textResponses"></div>
        </div>

        <div class="result-section">
            <h3>Data Results</h3>
            <div id="dataResults"></div>
        </div>

        <div class="result-section">
            <h3>Visualization</h3>
            <div id="visualization"></div>
            <div id="visualizationError" class="text-danger"></div>
        </div>

        <div class="card mt-4" id="analysisCard" style="display: none;">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Analysis</h5>
            </div>
            <div class="card-body">
                <div id="analysisContent" class="analysis-content"></div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="/" class="btn btn-primary">Ask Another Question</a>
        </div>
    </div>

    <script>
        // Load the original query and results from session storage
        window.onload = function() {
            const query = sessionStorage.getItem('userQuery');
            const results = JSON.parse(sessionStorage.getItem('queryResults'));
            
            document.getElementById('originalQuery').textContent = query;
            
            // Process each result
            results.forEach(result => {
                console.log("Processing result:", result);
                
                if (!result || !result.type) {
                    console.error("Invalid result format:", result);
                    return;
                }
                
                switch(result.type) {
                    case 'text':
                        const textDiv = document.createElement('div');
                        textDiv.className = 'text-response';
                        textDiv.textContent = result.content;
                        document.getElementById('textResponses').appendChild(textDiv);
                        break;
                    case 'dataframe':
                        try {
                            const dataContent = typeof result.content === 'string' 
                                ? JSON.parse(result.content) 
                                : result.content;
                            displayDataFrame(dataContent);
                        } catch (e) {
                            console.error("Error parsing dataframe:", e);
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'text-response text-danger';
                            errorDiv.textContent = "Error displaying data: " + e.message;
                            document.getElementById('dataResults').appendChild(errorDiv);
                        }
                        break;
                    case 'json':
                        try {
                            console.log("Rendering visualization from JSON:", result.content);
                            let vizContent;
                            
                            if (typeof result.content === 'string') {
                                try {
                                    vizContent = JSON.parse(result.content);
                                } catch (parseError) {
                                    console.error("Error parsing JSON string:", parseError);
                                    throw new Error("Invalid JSON format: " + parseError.message);
                                }
                            } else {
                                vizContent = result.content;
                            }
                            
                            console.log("Parsed visualization content:", vizContent);
                            
                            if (!vizContent || !vizContent.data) {
                                throw new Error("Visualization data is missing or invalid");
                            }
                            
                            // Create a new div for this visualization
                            const vizDiv = document.getElementById('visualization');
                            
                            // Render the plot
                            Plotly.newPlot(vizDiv, vizContent.data, vizContent.layout || {});
                            console.log("Visualization rendered successfully");
                        } catch (e) {
                            console.error("Error rendering visualization:", e);
                            const errorDiv = document.createElement('div');
                            errorDiv.className = 'text-response text-danger';
                            errorDiv.textContent = "Error displaying visualization: " + e.message;
                            document.getElementById('visualizationError').appendChild(errorDiv);
                        }
                        break;
                    case 'visualization':
                        // This handles the case where the visualization code is returned
                        // but not the actual JSON data
                        console.log("Visualization code received but not rendered");
                        break;
                    case 'error':
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'text-response text-danger';
                        errorDiv.textContent = result.content;
                        document.getElementById('textResponses').appendChild(errorDiv);
                        break;
                    case 'analysis':
                        const analysisCard = document.getElementById('analysisCard');
                        const analysisContent = document.getElementById('analysisContent');
                        
                        // Display the analysis
                        analysisContent.innerHTML = result.content.replace(/\n/g, '<br>');
                        analysisCard.style.display = 'block';
                        break;
                    default:
                        console.warn("Unknown result type:", result.type);
                }
            });
        }

        function displayDataFrame(data) {
            if (!data || data.length === 0) {
                document.getElementById('dataResults').innerHTML = 'No data available';
                return;
            }

            const table = document.createElement('table');
            table.className = 'table table-striped';

            // Create header
            const thead = document.createElement('thead');
            const headerRow = document.createElement('tr');
            Object.keys(data[0]).forEach(key => {
                const th = document.createElement('th');
                th.textContent = key;
                headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);
            table.appendChild(thead);

            // Create body
            const tbody = document.createElement('tbody');
            data.forEach(row => {
                const tr = document.createElement('tr');
                Object.values(row).forEach(value => {
                    const td = document.createElement('td');
                    td.textContent = value;
                    tr.appendChild(td);
                });
                tbody.appendChild(tr);
            });
            table.appendChild(tbody);

            document.getElementById('dataResults').appendChild(table);
        }
    </script>
</body>

