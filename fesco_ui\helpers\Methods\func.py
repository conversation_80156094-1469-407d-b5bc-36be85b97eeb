import datetime
import json
import os
import shutil
import sys
import time
import warnings
from pathlib import Path
from typing import Any, Dict, List, Tuple
from pytz import utc
import numpy as np
import pandas as pd
import psycopg2
import sqlalchemy
from dateutil.parser import parse

pd.set_option('display.max_colwidth', None)
warnings.filterwarnings("ignore")


def timing_decorator(decimal_points=3, delay_unit='s'):
    def wrapper(func):
        def inner_wrapper(*args, **kwargs):
            start_time = time.monotonic()
            result = func(*args, **kwargs)
            end_time = time.monotonic()
            delay_time = end_time - start_time

            if delay_unit == 'm':
                delay_time_str = f"{delay_time / 60:.{decimal_points}f} m"
            elif delay_unit == 's':
                delay_time_str = f"{delay_time:.{decimal_points}f} s"
            elif delay_unit == 'ms':
                delay_time_str = f"{delay_time * 1000:.{decimal_points}f} ms"
            elif delay_unit == 'us':
                delay_time_str = f"{delay_time * 1000000:.{decimal_points}f} us"
            else:
                raise ValueError(f"Invalid delay_unit: {delay_unit}")
            
            # print(f"-----------> {func.__name__} Executed in {delay_time_str}")
            return result
        return inner_wrapper
    return wrapper

@timing_decorator(decimal_points=5, delay_unit='us')
def read_config(filename):
    try:
        with open(filename, "r") as file:
            config = json.load(file)
            return config
    except FileNotFoundError:
        print(f"Error: File {filename} not found.")
    except json.JSONDecodeError:
        print(f"Error: File {filename} contains invalid JSON.")
    except Exception as e:
        print(f"Exception: {e}")  

@timing_decorator(decimal_points=5, delay_unit='us')
def get_dates_of_previous_and_two_months_ago():
    today = datetime.date.today()
    last_month = today.replace(day=1) - datetime.timedelta(days=1)
    two_months_ago = last_month.replace(day=1) - datetime.timedelta(days=1)
    first_day_of_last_month = last_month.replace(day=1)
    first_day_of_two_months_ago = two_months_ago.replace(day=1)
    first_day_of_month = today.replace(day=1)

    return (first_day_of_last_month.strftime("%Y-%m-%d"),
            last_month.strftime("%Y-%m-%d"),
            first_day_of_two_months_ago.strftime("%Y-%m-%d"),
            two_months_ago.strftime("%Y-%m-%d"),
            first_day_of_month.strftime("%Y-%m-%d"))

@timing_decorator(decimal_points=2, delay_unit='ms')
def create_directory(path, directory):
    try:   
        full_path = os.path.join(path, directory)
        if not os.path.exists(full_path):
            os.mkdir(full_path)
            return True
        else:
            # print(f"Directory {full_path} exists.")
            return True
    except Exception as e:
        print(f"Could not create directory: {e}")
        return False

def delete_directory(path):
    """
    Deletes the directory at the given path and its contents (including subdirectories and files).
    """
    try:
        shutil.rmtree(path)
        print(f"Directory at {path} deleted successfully.")
    except Exception as e:
        print(f"Error deleting directory: {e}")

def calculate_missing(site_df: pd.DataFrame) -> Dict[str, float]:
    missing_data = {}
    try:
        cm_cols = site_df.filter(regex='_cm$').columns
        missing_data = dict(zip([col[:-3] for col in cm_cols], round(site_df[cm_cols].isna().mean() * 100, 2)))
    except Exception as e:
        print(f"Error calculating missing data: {e}")
    return missing_data

def calculate_missing_data(df: pd.DataFrame, rca: Dict[str, Any], rca_type: str) -> pd.DataFrame:
    missing_data_dict = df.groupby('siteid').apply(calculate_missing).to_dict()
    df_list = []
    for site_id, missing_data in missing_data_dict.items():
        rca_copy = rca.copy()
        rca_copy["Missing Data"] = missing_data
        df_site = pd.DataFrame({'siteid': [site_id], rca_type: [json.dumps(rca_copy)]})
        df_list.append(df_site)
    return pd.concat(df_list, ignore_index=True)


@timing_decorator(decimal_points=2, delay_unit='s')
def merged_dictionary(df1, df2):
    merged_df = pd.merge(df1, df2, on='siteid', how='outer', suffixes=('', '_monthly'))
    cols = merged_df.columns
    for index, row in merged_df.iterrows():
        dict1 = json.loads(row[cols[1]].replace('NaN', 'null') if isinstance(row[cols[1]], str) else '{}')
        dict2 = json.loads(row[cols[2]].replace('NaN', 'null') if isinstance(row[cols[2]], str) else '{}')

        def make_dict_empty(d):
            for k, v in d.items():
                if isinstance(v, dict):
                    make_dict_empty(v)
                elif isinstance(v, list):
                    d[k] = []
                else:
                    d[k] = '' if isinstance(v, str) else 0.0
            return d

        if not dict1:
            prev_row = merged_df.iloc[index-1]
            prev_dict1 = json.loads(prev_row[cols[1]].replace('NaN', 'null') if isinstance(prev_row[cols[1]], str) else '{}')
            dict1 = make_dict_empty(prev_dict1)
        if not dict2:
            prev_row = merged_df.iloc[index-1]
            prev_dict2 = json.loads(prev_row[cols[2]].replace('NaN', 'null') if isinstance(prev_row[cols[2]], str) else '{}')
            dict2 = make_dict_empty(prev_dict2)

        for key, value in dict2.items():
            if key in dict1:
                if isinstance(dict1[key], dict) and isinstance(value, dict):
                    dict1[key].update(value)
                else:
                    dict1[key] = value
            else:
                dict1[key] = value
        merged_df.at[index, cols[1]] = json.dumps(dict1)
    del merged_df[cols[2]]
    return merged_df

@timing_decorator(decimal_points=2, delay_unit='ms')    
def merge_and_average(df1, df2, path, directory, file):
    # Get all columns that end with _cm and _pm
    if 'date' in df1.columns:
        df1.drop('date', axis=1, inplace=True)
    if 'date' in df2.columns:
        df2.drop('date', axis=1, inplace=True)    
        
    date_cols_1 = df1.filter(regex='date').columns
    cm_pm_cols_1 = df1.filter(regex='_cm$|_pm$').columns
    cm_pm_cols_1 = cm_pm_cols_1[~cm_pm_cols_1.str.contains('date')]

    # Take the mean of all cm/pm columns based on siteid for each dataframe
    df1 = df1.groupby('siteid')[cm_pm_cols_1].mean().reset_index()

    # Merge the date columns back into the mean dataframes
    df1 = pd.merge(df1[['siteid'] + list(date_cols_1)], df1, on='siteid')

    # Drop duplicates based on siteid
    df1.drop_duplicates(subset='siteid', inplace=True)

    if 'unixtime' not in df2.columns:
        # Merge the two dataframes based on siteid
        merged_df = pd.merge(df1, df2, on='siteid', how='outer')    
        merged_df.set_index('siteid', inplace=True)
        cm_pm_cols = sorted(merged_df.columns)
        merged_df = merged_df[cm_pm_cols]

        try: 
            # Check if each column ending with _cm has a corresponding column ending with _pm
            for col in [c for c in cm_pm_cols if c.endswith('_cm')]:
                if col[:-2] + 'pm' not in cm_pm_cols:
                    raise ValueError(f"Error: {col[:-2] + 'pm'} not found for {col}")
        except ValueError as e:
            print(f"CM_PM Columns Pairing Error : {e}")
            raise
        merged_df.to_csv(os.path.join(path, directory, f"{file}_Merged_Data.csv"))
        return merged_df
    else:
        df1.set_index('siteid', inplace=True)
        cm_pm_cols = sorted(df1.columns)
        df1 = df1[cm_pm_cols]

        try: 
            # Check if each column ending with _cm has a corresponding column ending with _pm
            for col in [c for c in cm_pm_cols if c.endswith('_cm')]:
                if col[:-2] + 'pm' not in cm_pm_cols:
                    raise ValueError(f"Error: {col[:-2] + 'pm'} not found for {col}")
        except ValueError as e:
            print(f"CM_PM Columns Pairing Error : {e}")
            raise
        # Drop duplicates based on siteid
        df2.drop_duplicates(subset='siteid', inplace=True)
        df1 = pd.merge(df1, df2, on='siteid', how='outer')    
        df1.drop(columns='unixtime', inplace=True)
        df1.rename(columns={'displaypoint': 'alarm'}, inplace=True)
        df1.to_csv(os.path.join(path, directory, f"{file}_Merged_Data.csv"), index=False)
        return df1

@timing_decorator(decimal_points=2, delay_unit='s')    
def merge_combined_and_daily(combined, path, directory, file):        
    # sort dataframe based on siteid and date columns
    df1 = pd.read_csv(os.path.join(path, directory, f"{file}_data_daily.csv"), na_values='')    
    df1 = df1.sort_values(['siteid', 'date'])
    df1.drop(columns=['date'], inplace=True)
    dict_list = df1.groupby('siteid').apply(lambda x: x.iloc[:, 1:].to_dict('list')).to_dict()
    new_df = pd.DataFrame.from_dict(dict_list, orient='index')
    new_df = new_df.reset_index().rename(columns={'index': 'siteid'})
    new_df = pd.merge(combined, new_df, on='siteid', how='inner', suffixes=('','_daily'))    
    new_df.to_csv(os.path.join(path, directory, f"{file}_Merged_Data.csv"), index=False)
    return new_df

@timing_decorator(decimal_points=2, delay_unit='ms')    
def parsing(df,path,directory,file):
    df.drop_duplicates(subset=['siteid'], inplace=True)
    df.replace(to_replace=[None,'NULL','None','nan','Null','np.nan',np.nan,'Nan','NaN', 0, 0.0, '0', '0.0', 'DC SHARED', 'DC', 'DC Shared', 'PUBLIC SHARED', 'Public Shared'], value=np.nan, inplace=True)

    date_cols       =   df.filter(regex='date').columns        
    daily_cols      =   df.filter(regex='_daily$').columns
    numeric_cols    =   df.filter(regex='_cm$|_pm$').columns
    numeric_cols    =   [col for col in numeric_cols if col not in date_cols]  
    numeric_cols    =   [col for col in numeric_cols if col not in daily_cols]  

    # Define the list of methods in the order of their efficiency
    date_methods = [
        lambda x: pd.to_datetime(x, infer_datetime_format=True, cache=True, errors='coerce').dt.date.astype(str),
        lambda x: pd.to_datetime(x, infer_datetime_format=True, cache=True, errors='coerce').dt.strftime('%Y-%m-%d'),
        lambda x: pd.to_datetime(x, format='%d/%m/%Y', cache=True, errors='coerce').dt.strftime('%Y-%m-%d'),
        lambda x: pd.to_datetime(x, format='%Y-%m-%d', cache=True, errors='coerce').dt.date.astype(str),
        lambda x: pd.to_datetime(x, format='%d-%m-%Y', cache=True, errors='coerce').dt.strftime('%Y-%m-%d'),
        lambda x: pd.to_datetime(x, format='%m-%d-%Y', cache=True, errors='coerce').dt.strftime('%Y-%m-%d'),
        lambda x: pd.to_datetime(x, format='%Y/%m/%d', cache=True, errors='coerce').dt.strftime('%Y-%m-%d'),
        lambda x: pd.to_datetime(x, format='%m/%d/%Y', cache=True, errors='coerce').dt.strftime('%Y-%m-%d'),
        lambda x: pd.to_datetime(x, errors='coerce', cache=True, format='%Y%m%d').dt.date.astype(str),
        lambda x: pd.to_datetime(x, errors='coerce', cache=True, format='%m%d%Y').dt.strftime('%Y-%m-%d'),
        lambda x: pd.to_datetime(x, errors='coerce').dt.date.astype(str),
        lambda x: pd.to_datetime(x, errors='coerce').dt.strftime('%Y-%m-%d'),
        lambda x: parse(x, ignoretz=True, dayfirst=False).strftime('%Y-%m-%d'),
    ]
    
    # Define the list of methods for numerical columns in the order of their efficiency
    num_methods = [
        lambda x: pd.to_numeric(x, errors='coerce'),
        lambda x: pd.to_numeric(x.astype(str).str.replace(',', ''), errors='coerce'),
        lambda x: pd.to_numeric(x.astype(str).str.replace(' ', ''), errors='coerce'),
        lambda x: pd.to_numeric(x.astype(str).str.replace('$', ''), errors='coerce'),
    ]

    # Define a function to try all methods to convert a single column
    def try_methods(column, methods):
        for method in methods:
            try:
                return method(column)
            except (ValueError, TypeError):
                print(f"got exceptions {column}")
                continue
        return column
        
    # Convert all date and numeric columns using the try_methods() function
    try:
        for col in date_cols:
            if not df[col].isna().all():
                df[col] = try_methods(df[col], date_methods)
    except Exception as e:
        print(f"Error Parsing Date Columns: {e}")

    try:
        for col in numeric_cols:
            if not df[col].isna().all():
                df[col] = try_methods(df[col], num_methods)
                df[col] = df[col].round(2)
    except Exception as e:
        print(f"Error Parsing Numeric Columns: {e}")

    # Check if any columns failed to convert to the desired format
    for col in date_cols:
        if not pd.api.types.is_datetime64_dtype(df[col]):
            Exception("Error: Failed to convert {} column.".format(col))
    for col in numeric_cols:
        if not pd.api.types.is_numeric_dtype(df[col]):
            Exception("Error: Failed to convert {} column.".format(col))

    df.replace(to_replace=['NaT'], value=np.nan, inplace=True)
    df.to_csv(os.path.join(path, directory, file), index=False)

    return df

def parse_list(lst):
    if isinstance(lst, str):
        lst = lst.strip("[]").split(", ")
        lst = [round(float(x),2) if x != 'nan' else np.nan for x in lst]
    return lst
    


def fixed_values(df,dic):
    column          =   list(dic.keys())
    for col in column:
        df[col]     =   dic[col]
    return df


def convert_to_float(val):
    try:

        if isinstance(val,pd.Series):
            val = val.iloc[0]
        if val in [None, np.nan, 'N/A', 'NULL', 'None', 'nan', 'Null', 'np.nan', 'Nan', 'NaN', 'DC SHARED', 'Theft', 'null', 123456789, '123456789', "", "  "]:
            return None
        elif isinstance(val, str):
            val = val.replace(",", ".")
        return float(val) if val is not None else None
    except:
        return None
