from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, Depends
from fastapi.responses import JSONResponse, HTMLResponse, StreamingResponse
from fastapi.templating import Jin<PERSON>2Templates
from pydantic import BaseModel
import json
import traceback
import asyncio
from typing import List, Dict, Optional

from services.router_service import RouterService, QueryType
from services.sql_service import SQLService
from services.rag_service import RagService
from services.chat_service import ChatService
from services.fesco_demo import analyze_fesco_data

# Define request models
class QueryRequest(BaseModel):
    prompt  : str
    user_id : str = None 
    context : Optional[List[Dict[str, str]]] = None


# Define response models
class ErrorResponse(BaseModel):
    type    : str = "error"
    message : str

def register_routes(app):
    # Initialize services
    router_service = RouterService()
    sql_service = SQLService()
    rag_service = RagService()
    chat_service = ChatService()
    
    @app.get("/", response_class=HTMLResponse)
    async def index(request: Request):
        return app.templates.TemplateResponse("main.html", {"request": request})
    
    @app.get("/chat_view", response_class=HTMLResponse)
    async def chat_view(request: Request):
        return app.templates.TemplateResponse("chat_template.html", {"request": request})
    
    @app.get("/sql_view", response_class=HTMLResponse)
    async def sql_view(request: Request):
        return app.templates.TemplateResponse("sql_template.html", {"request": request})
    
    @app.get("/fesco", response_class=HTMLResponse)
    async def fesco_view(request: Request):
        """
        Serves the FESCO analysis HTML template.
        """
        return app.templates.TemplateResponse("fesco_analysis.html", {"request": request})
    
    # @app.post("/api/process")
    # async def process_query(query: QueryRequest):
    #     try:
    #         user_query = query.prompt
    #         user_id = query.user_id
            
    #         # Get classification from router (now async)
    #         classification = await router_service.classify_query(user_query)
            
    #         # Single consolidated debug log
    #         print(f"\n=== API Request Processing ===")
    #         print(f"User ID: {user_id if user_id else 'Anonymous'}")
    #         print(f"Query: {user_query}")
    #         print(f"Classification: {classification.query_type.value}")
    #         print(f"Confidence: {classification.confidence}")
    #         print(f"Requires DB: {classification.requires_db}")
            
    #         if classification.query_type == QueryType.DATABASE:
    #             print(f"Action: Executing database query")
    #             # Process with SQL service
    #             sql_results = []
    #             async for result in sql_service.forward_pass(user_query):
    #                 if isinstance(result, str):
    #                     try:
    #                         # Try to parse as JSON
    #                         result_dict = json.loads(result)
    #                         sql_results.append(result_dict)
    #                     except json.JSONDecodeError:
    #                         # If not valid JSON, wrap as text
    #                         sql_results.append({"type": "text", "content": result})
    #                 else:
    #                     sql_results.append(result)
            
    #             print(f"Response: Returning {len(sql_results)} SQL results")
    #             return {
    #                 'type': 'db_query',
    #                 'results': sql_results
    #             }
            
    #         elif classification.query_type == QueryType.KNOWLEDGE:
    #             print(f"Action: Retrieving knowledge with RAG")
    #             # Process with RAG service (now async)
    #             rag_results = []
    #             try:
    #                 async for result in rag_service.handle_rag(user_query):
    #                     if isinstance(result, dict) and 'content' in result:
    #                         rag_results.append(result)
    #                     else:
    #                         rag_results.append({"type": "text", "content": str(result)})
    #             except Exception as e:
    #                 print(f"RAG error: {str(e)}")
    #                 rag_results.append({"type": "text", "content": f"I'm sorry, I encountered an error while processing your request. Please try again later."})
            
    #             print(f"Response: Returning {len(rag_results)} RAG results")
    #             return {
    #                 'type': 'chat',  # Keep as 'chat' for frontend compatibility
    #                 'results': rag_results
    #             }

    #         elif classification.query_type == QueryType.CONVERSATIONAL:
    #             print(f"Action: Handling conversational query")
    #             # Process with chat service (now async)
    #             chat_results = []
    #             try:
    #                 async for result in chat_service.handle_conversational(user_query):
    #                     if isinstance(result, dict) and 'content' in result:
    #                         chat_results.append(result)
    #                     else:
    #                         chat_results.append({"type": "text", "content": str(result)})
    #             except Exception as e:
    #                 print(f"Chat error: {str(e)}")
    #                 chat_results.append({"type": "text", "content": f"I'm sorry, I encountered an error while processing your request. Please try again later."})
            
    #             print(f"Response: Returning {len(chat_results)} chat results")
    #             return {
    #                 'type': 'chat',
    #                 'results': chat_results
    #             }
    #     except Exception as e:
    #         print(f"\n=== API Error ===")
    #         print(f"User ID: {query.user_id}")
    #         print(f"Query: {query.prompt}")
    #         print(f"Error: {str(e)}")
    #         print(traceback.format_exc())
            
    #         return JSONResponse(
    #             status_code=500,
    #             content={"type": "error", "message": f"An error occurred: {str(e)}"}
    #         )

    # @app.post("/api/fesco-analysis")
    # async def fesco_analysis(request: Request):
    #     """
    #     Endpoint for FESCO data analysis demo.
    #     Modified to return a single JSON response instead of streaming.
    #     """
    #     try:
    #         # Parse the request body
    #         data = await request.json()
    #         user_query = data.get("prompt", "Analyze this data and identify key patterns and insights")
            
    #         # Collect all results from the generator
    #         results = []
    #         async for result in analyze_fesco_data(user_query):
    #             results.append(result)
            
    #         # Return a single JSON response with all results
    #         return JSONResponse(
    #             content={
    #                 "type": "fesco_analysis",
    #                 "results": results
    #             }
    #         )
    #     except Exception as e:
    #         print(f"Error in fesco-analysis endpoint: {str(e)}")
    #         return JSONResponse(
    #             status_code=500,
    #             content={"error": f"An error occurred: {str(e)}"}
    #         )


    # This endpoint is for streaming purposes  
    @app.post("/api/stream-fesco")
    async def stream_fesco_analysis(query: QueryRequest):
        """
        Streams the FESCO analysis results as JSON as soon as they are available.
        """
        try:
            user_query = query.prompt
            context    = query.context or [] 
            print(f"context : {context}")
            async def response_stream():
                async for result in analyze_fesco_data(user_query, context):
                    yield json.dumps(result) + "\n"

            return StreamingResponse(
                response_stream(),
                media_type = "application/x-ndjson"
            )
        except Exception as e:
            print(f"Error in streaming FESCO API: {str(e)}")

            return JSONResponse(
                status_code = 500,
                content     = {
                    "type"    : "error",
                    "results" : [
                        {
                            "type"    : "text",
                            "content" : "The model is busy at the moment. Please try again later."
                        }
                    ]
                } 
            )