import asyncio
import sys
import os
from pathlib import Path

# Add the parent directory to sys.path to import from app
sys.path.append(str(Path(__file__).parent.parent.parent))

from app.services.qdrant_service import QdrantService

async def test_query_retrieval():
    """
    Test the retrieve_similar_queries function with various user queries.
    """
    qdrant_service = QdrantService()
    
    # Test queries
    test_queries = [
        "How many sites have good communication?",
        "Count sites by brand",
        "Show me sites grouped by rectifier installation year",
        "What's the distribution of sites by transformer capacity?",
        "Count sites with different DG capacity ranges"
    ]
    
    for query in test_queries:
        print(f"\n\n=== Testing query: '{query}' ===")
        
        # Get similar queries
        similar_queries = await qdrant_service.retrieve_similar_queries(query, top_k=2)
        
        print(f"Found {len(similar_queries)} similar queries")
        
        # Print results
        for i, result in enumerate(similar_queries):
            print(f"\nResult {i+1} (Score: {result['score']:.4f}):")
            print(f"Natural Language: {result['natural_language']}")
            print(f"SQL Query: {result['sql_query'][:100]}..." if len(result['sql_query']) > 100 else f"SQL Query: {result['sql_query']}")
            print(f"Description: {result['description'][:100]}..." if len(result['description']) > 100 else f"Description: {result['description']}")

if __name__ == "__main__":
    asyncio.run(test_query_retrieval())